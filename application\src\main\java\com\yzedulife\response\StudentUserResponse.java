package com.yzedulife.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;



/**
 * 学生用户响应
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "学生用户响应")
public class StudentUserResponse {

    @Schema(description = "学生ID")
    private Long id;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "学号")
    private String studentNumber;

    @Schema(description = "班级ID")
    private Long classId;
}
