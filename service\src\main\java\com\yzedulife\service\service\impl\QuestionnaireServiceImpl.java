package com.yzedulife.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.QuestionnaireDTO;
import com.yzedulife.service.entity.Questionnaire;
import com.yzedulife.service.mapper.QuestionnaireMapper;
import com.yzedulife.service.service.QuestionnaireService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 问卷服务实现类
 */
@Service
@Transactional
public class QuestionnaireServiceImpl implements QuestionnaireService {

    @Autowired
    private QuestionnaireMapper questionnaireMapper;

    @Override
    public QuestionnaireDTO getById(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("问卷ID不能为空");
        }
        Questionnaire questionnaire = questionnaireMapper.selectById(id);
        if (questionnaire == null) {
            throw new BusinessException("问卷不存在");
        }
        return questionnaire.toDTO();
    }

    @Override
    public QuestionnaireDTO create(QuestionnaireDTO questionnaireDTO) throws BusinessException {
        if (questionnaireDTO == null) {
            throw new BusinessException("问卷信息不能为空");
        }
        if (!StringUtils.hasText(questionnaireDTO.getTitle())) {
            throw new BusinessException("问卷标题不能为空");
        }
        if (!StringUtils.hasText(questionnaireDTO.getTargetAudience())) {
            throw new BusinessException("目标受众不能为空");
        }
        if (questionnaireDTO.getCreatorId() == null) {
            throw new BusinessException("创建者ID不能为空");
        }
        
        Questionnaire questionnaire = questionnaireDTO.toEntity();
        questionnaire.setId(null); // 确保是新增
        if (questionnaire.getStatus() == null) {
            questionnaire.setStatus(0); // 默认为草稿状态
        }
        
        int result = questionnaireMapper.insert(questionnaire);
        if (result <= 0) {
            throw new BusinessException("创建问卷失败");
        }
        return questionnaire.toDTO();
    }

    @Override
    public QuestionnaireDTO update(QuestionnaireDTO questionnaireDTO) throws BusinessException {
        if (questionnaireDTO == null || questionnaireDTO.getId() == null) {
            throw new BusinessException("问卷ID不能为空");
        }
        
        // 检查问卷是否存在
        Questionnaire existingQuestionnaire = questionnaireMapper.selectById(questionnaireDTO.getId());
        if (existingQuestionnaire == null) {
            throw new BusinessException("问卷不存在");
        }
        
        // 检查问卷状态，已发布的问卷不能修改基本信息
        if (existingQuestionnaire.getStatus() == 1) {
            throw new BusinessException("已发布的问卷不能修改");
        }
        
        Questionnaire questionnaire = questionnaireDTO.toEntity();
        int result = questionnaireMapper.updateById(questionnaire);
        if (result <= 0) {
            throw new BusinessException("更新问卷失败");
        }
        return questionnaireMapper.selectById(questionnaire.getId()).toDTO();
    }

    @Override
    public Boolean deleteById(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("问卷ID不能为空");
        }
        
        Questionnaire questionnaire = questionnaireMapper.selectById(id);
        if (questionnaire == null) {
            throw new BusinessException("问卷不存在");
        }
        
        // 检查问卷状态，已发布的问卷不能删除
        if (questionnaire.getStatus() == 1) {
            throw new BusinessException("已发布的问卷不能删除");
        }
        
        int result = questionnaireMapper.deleteById(id);
        return result > 0;
    }

    @Override
    public List<QuestionnaireDTO> getAll() throws BusinessException {
        List<Questionnaire> questionnaires = questionnaireMapper.selectList(new LambdaQueryWrapper<Questionnaire>()
                .orderByDesc(Questionnaire::getId));
        return questionnaires.stream().map(Questionnaire::toDTO).collect(java.util.stream.Collectors.toList());
    }

    @Override
    public List<QuestionnaireDTO> getByCreatorId(Long creatorId) throws BusinessException {
        if (creatorId == null) {
            throw new BusinessException("创建者ID不能为空");
        }
        List<Questionnaire> questionnaires = questionnaireMapper.selectList(new LambdaQueryWrapper<Questionnaire>()
                .eq(Questionnaire::getCreatorId, creatorId)
                .orderByDesc(Questionnaire::getId));
        return questionnaires.stream().map(Questionnaire::toDTO).collect(java.util.stream.Collectors.toList());
    }

    @Override
    public Boolean publish(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("问卷ID不能为空");
        }
        
        Questionnaire questionnaire = questionnaireMapper.selectById(id);
        if (questionnaire == null) {
            throw new BusinessException("问卷不存在");
        }
        
        if (questionnaire.getStatus() == 1) {
            throw new BusinessException("问卷已经是发布状态");
        }
        
        questionnaire.setStatus(1); // 设置为发布状态
        int result = questionnaireMapper.updateById(questionnaire);
        return result > 0;
    }

    @Override
    public Boolean close(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("问卷ID不能为空");
        }
        
        Questionnaire questionnaire = questionnaireMapper.selectById(id);
        if (questionnaire == null) {
            throw new BusinessException("问卷不存在");
        }
        
        if (questionnaire.getStatus() == 2) {
            throw new BusinessException("问卷已经是关闭状态");
        }
        
        questionnaire.setStatus(2); // 设置为关闭状态
        int result = questionnaireMapper.updateById(questionnaire);
        return result > 0;
    }

    @Override
    public QuestionnaireDTO copy(Long id, Long newCreatorId) throws BusinessException {
        if (id == null) {
            throw new BusinessException("问卷ID不能为空");
        }
        if (newCreatorId == null) {
            throw new BusinessException("新创建者ID不能为空");
        }
        
        Questionnaire originalQuestionnaire = questionnaireMapper.selectById(id);
        if (originalQuestionnaire == null) {
            throw new BusinessException("原问卷不存在");
        }
        
        // 创建副本
        Questionnaire copyQuestionnaire = new Questionnaire();
        copyQuestionnaire.setTitle(originalQuestionnaire.getTitle() + " - 副本");
        copyQuestionnaire.setTargetAudience(originalQuestionnaire.getTargetAudience());
        copyQuestionnaire.setStatus(0); // 副本默认为草稿状态
        copyQuestionnaire.setCreatorId(newCreatorId);
        
        int result = questionnaireMapper.insert(copyQuestionnaire);
        if (result <= 0) {
            throw new BusinessException("复制问卷失败");
        }
        
        return copyQuestionnaire.toDTO();
    }
}
