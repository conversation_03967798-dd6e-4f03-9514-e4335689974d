package com.yzedulife.service.dto;

import com.yzedulife.service.convert.QuestionOptionConvert;
import com.yzedulife.service.entity.QuestionOption;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 题目选项DTO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QuestionOptionDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 选项ID
     */
    private Long id;

    /**
     * 所属题目ID
     */
    private Long questionId;

    /**
     * 选项类型 (TEXT/IMAGE)
     */
    private String optionType;

    /**
     * 内容 (文本/URL)
     */
    private String content;

    /**
     * 选项代号
     */
    private String optionCode;

    public QuestionOption toEntity() {
        return QuestionOptionConvert.INSTANCE.dto2entity(this);
    }
}
