package com.yzedulife.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.AdminDTO;
import com.yzedulife.service.entity.Admin;
import com.yzedulife.service.mapper.AdminMapper;
import com.yzedulife.service.service.impl.AdminServiceImpl;
import com.yzedulife.util.TestDataFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("管理员服务测试")
class AdminServiceTest {

    @Mock
    private AdminMapper adminMapper;

    @InjectMocks
    private AdminServiceImpl adminService;

    private Admin testAdmin;
    private AdminDTO testAdminDTO;

    @BeforeEach
    void setUp() {
        testAdminDTO = TestDataFactory.createAdminDTO();
        testAdmin = new Admin();
        testAdmin.setId(testAdminDTO.getId());
        testAdmin.setUsername(testAdminDTO.getUsername());
        testAdmin.setPassword(testAdminDTO.getPassword());
        testAdmin.setRole(testAdminDTO.getRole());
    }

    @Test
    @DisplayName("根据ID获取管理员 - 成功")
    void getById_Success() throws BusinessException {
        // Given
        when(adminMapper.selectById(1L)).thenReturn(testAdmin);

        // When
        AdminDTO result = adminService.getById(1L);

        // Then
        assertNotNull(result);
        assertEquals(testAdminDTO.getId(), result.getId());
        assertEquals(testAdminDTO.getUsername(), result.getUsername());
        assertEquals(testAdminDTO.getPassword(), result.getPassword());
        assertEquals(testAdminDTO.getRole(), result.getRole());

        verify(adminMapper).selectById(1L);
    }

    @Test
    @DisplayName("根据ID获取管理员 - ID为空")
    void getById_NullId() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> adminService.getById(null));
        
        assertEquals("管理员ID不能为空", exception.getMessage());
        verify(adminMapper, never()).selectById(any(Long.class));
    }

    @Test
    @DisplayName("根据ID获取管理员 - 管理员不存在")
    void getById_AdminNotFound() {
        // Given
        when(adminMapper.selectById(999L)).thenReturn(null);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> adminService.getById(999L));
        
        assertEquals("管理员不存在", exception.getMessage());
        verify(adminMapper).selectById(999L);
    }

    @Test
    @DisplayName("根据用户名获取管理员 - 成功")
    void getByUsername_Success() throws BusinessException {
        // Given
        when(adminMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(testAdmin);

        // When
        AdminDTO result = adminService.getByUsername("testadmin");

        // Then
        assertNotNull(result);
        assertEquals(testAdminDTO.getUsername(), result.getUsername());

        verify(adminMapper).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("根据用户名获取管理员 - 用户名为空")
    void getByUsername_EmptyUsername() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> adminService.getByUsername(""));
        
        assertEquals("用户名不能为空", exception.getMessage());
        verify(adminMapper, never()).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("根据用户名获取管理员 - 用户名为null")
    void getByUsername_NullUsername() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> adminService.getByUsername(null));
        
        assertEquals("用户名不能为空", exception.getMessage());
        verify(adminMapper, never()).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("根据用户名获取管理员 - 管理员不存在")
    void getByUsername_AdminNotFound() {
        // Given
        when(adminMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> adminService.getByUsername("nonexistent"));
        
        assertEquals("管理员不存在", exception.getMessage());
        verify(adminMapper).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("验证登录 - 成功")
    void validateLogin_Success() throws BusinessException {
        // Given
        when(adminMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(testAdmin);

        // When
        AdminDTO result = adminService.validateLogin("testadmin", "testpassword");

        // Then
        assertNotNull(result);
        assertEquals(testAdminDTO.getUsername(), result.getUsername());

        verify(adminMapper).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("验证登录 - 用户名为空")
    void validateLogin_EmptyUsername() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> adminService.validateLogin("", "password"));
        
        assertEquals("用户名和密码不能为空", exception.getMessage());
        verify(adminMapper, never()).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("验证登录 - 密码为空")
    void validateLogin_EmptyPassword() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> adminService.validateLogin("username", ""));
        
        assertEquals("用户名和密码不能为空", exception.getMessage());
        verify(adminMapper, never()).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("验证登录 - 用户名或密码错误")
    void validateLogin_InvalidCredentials() {
        // Given
        when(adminMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> adminService.validateLogin("wronguser", "wrongpass"));
        
        assertEquals("用户名或密码错误", exception.getMessage());
        verify(adminMapper).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("检查管理员是否存在 - 存在")
    void isExist_True() throws BusinessException {
        // Given
        when(adminMapper.selectById(1L)).thenReturn(testAdmin);

        // When
        Boolean result = adminService.isExist(1L);

        // Then
        assertTrue(result);
        verify(adminMapper).selectById(1L);
    }

    @Test
    @DisplayName("检查管理员是否存在 - 不存在")
    void isExist_False() throws BusinessException {
        // Given
        when(adminMapper.selectById(999L)).thenReturn(null);

        // When
        Boolean result = adminService.isExist(999L);

        // Then
        assertFalse(result);
        verify(adminMapper).selectById(999L);
    }

    @Test
    @DisplayName("检查管理员是否存在 - ID为空")
    void isExist_NullId() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> adminService.isExist(null));
        
        assertEquals("管理员ID不能为空", exception.getMessage());
        verify(adminMapper, never()).selectById(any(Long.class));
    }
}
