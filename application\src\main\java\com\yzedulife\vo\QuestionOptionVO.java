package com.yzedulife.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 题目选项请求VO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "题目选项请求VO")
public class QuestionOptionVO {
    
    @Schema(description = "选项ID（更新时需要）")
    private Long id;
    
    @Schema(description = "选项类型", example = "TEXT 或 IMAGE", required = true)
    @NotBlank(message = "选项类型不能为空")
    private String optionType;
    
    @Schema(description = "选项内容", required = true)
    @NotBlank(message = "选项内容不能为空")
    private String content;
    
    @Schema(description = "选项代号", example = "A, B, C, D", required = true)
    @NotBlank(message = "选项代号不能为空")
    private String optionCode;
}
