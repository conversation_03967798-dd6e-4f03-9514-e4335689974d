package com.yzedulife.controller;

import com.yzedulife.annotation.Token;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.common.util.MD5Util;
import com.yzedulife.response.Response;
import com.yzedulife.response.UploadResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Arrays;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/img")
@Tag(name = "图片模块")
public class ImageController {

    @Value("${file.upload.path:./uploads/images/}")
    private String uploadPath;

    // 允许的图片格式
    private static final List<String> ALLOWED_EXTENSIONS = Arrays.asList("jpg", "jpeg", "png", "gif", "bmp", "webp");
    
    // 最大文件大小：2MB
    private static final long MAX_FILE_SIZE = 2 * 1024 * 1024;

    @Token("admin")
    @Operation(summary = "图片上传")
    @PostMapping("/upload")
    public Response upload(@RequestParam("file") MultipartFile file) {
        try {
            // 检查文件是否为空
            if (file.isEmpty()) {
                return Response.error().msg("上传文件不能为空");
            }

            // 检查文件大小
            if (file.getSize() > MAX_FILE_SIZE) {
                return Response.error().msg("文件大小不能超过2MB");
            }

            // 获取原始文件名和扩展名
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.isEmpty()) {
                return Response.error().msg("文件名不能为空");
            }

            String extension = getFileExtension(originalFilename).toLowerCase();
            if (!ALLOWED_EXTENSIONS.contains(extension)) {
                return Response.error().msg("不支持的文件格式，仅支持：" + String.join(", ", ALLOWED_EXTENSIONS));
            }

            // 生成文件的MD5哈希值作为文件名
            String hash = MD5Util.getMd5(file);
            if (hash == null) {
                return Response.error().msg("文件处理失败");
            }

            String filename = hash + "." + extension;

            // 确保上传目录存在
            Path uploadDir = Paths.get(uploadPath);
            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
            }

            // 保存文件
            Path filePath = uploadDir.resolve(filename);
            
            // 如果文件已存在，直接返回路径（相同内容的文件不重复存储）
            if (!Files.exists(filePath)) {
                Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
                log.info("文件上传成功：{}", filename);
            } else {
                log.info("文件已存在，直接返回路径：{}", filename);
            }

            // 返回访问路径
            String url = "/img/" + hash;
            return Response.success().data(new UploadResponse(url));

        } catch (IOException e) {
            log.error("文件上传失败", e);
            return Response.error().msg("文件上传失败");
        } catch (Exception e) {
            log.error("文件上传异常", e);
            return Response.error().msg("文件上传异常");
        }
    }

    @Token("student other")
    @Operation(summary = "获取图片")
    @GetMapping("/{hash}")
    public ResponseEntity<Resource> getImage(@PathVariable String hash) {
        try {
            // 查找对应的文件
            Path uploadDir = Paths.get(uploadPath);
            Path filePath = null;
            
            // 遍历所有支持的扩展名查找文件
            for (String ext : ALLOWED_EXTENSIONS) {
                Path candidatePath = uploadDir.resolve(hash + "." + ext);
                if (Files.exists(candidatePath)) {
                    filePath = candidatePath;
                    break;
                }
            }

            if (filePath == null || !Files.exists(filePath)) {
                return ResponseEntity.notFound().build();
            }

            Resource resource = new UrlResource(filePath.toUri());
            if (!resource.exists() || !resource.isReadable()) {
                return ResponseEntity.notFound().build();
            }

            // 根据文件扩展名设置Content-Type
            String extension = getFileExtension(filePath.getFileName().toString()).toLowerCase();
            MediaType mediaType = getMediaTypeForExtension(extension);

            return ResponseEntity.ok()
                    .contentType(mediaType)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + filePath.getFileName().toString() + "\"")
                    .body(resource);

        } catch (Exception e) {
            log.error("获取图片失败：{}", hash, e);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return "";
        }
        return filename.substring(lastDotIndex + 1);
    }

    /**
     * 根据文件扩展名获取MediaType
     */
    private MediaType getMediaTypeForExtension(String extension) {
        switch (extension) {
            case "jpg":
            case "jpeg":
                return MediaType.IMAGE_JPEG;
            case "png":
                return MediaType.IMAGE_PNG;
            case "gif":
                return MediaType.IMAGE_GIF;
            case "bmp":
                return MediaType.valueOf("image/bmp");
            case "webp":
                return MediaType.valueOf("image/webp");
            default:
                return MediaType.APPLICATION_OCTET_STREAM;
        }
    }
}
