package com.yzedulife.convert;

import com.yzedulife.response.QuestionOptionResponse;
import com.yzedulife.response.QuestionResponse;
import com.yzedulife.service.dto.QuestionDTO;
import com.yzedulife.service.dto.QuestionOptionDTO;
import com.yzedulife.vo.QuestionOptionVO;
import com.yzedulife.vo.QuestionVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 题目应用层转换器
 */
@Mapper
public interface QuestionAppConvert {
    QuestionAppConvert INSTANCE = Mappers.getMapper(QuestionAppConvert.class);

    /**
     * VO转DTO
     */
    QuestionDTO vo2dto(QuestionVO vo);

    /**
     * DTO转Response
     */
    QuestionResponse dto2response(QuestionDTO dto);

    /**
     * QuestionOptionVO转DTO
     */
    QuestionOptionDTO optionVo2dto(QuestionOptionVO vo);

    /**
     * QuestionOptionDTO转Response
     */
    QuestionOptionResponse optionDto2response(QuestionOptionDTO dto);

    /**
     * QuestionOptionVO列表转DTO列表
     */
    List<QuestionOptionDTO> optionVo2dtoList(List<QuestionOptionVO> voList);

    /**
     * QuestionOptionDTO列表转Response列表
     */
    List<QuestionOptionResponse> optionDto2responseList(List<QuestionOptionDTO> dtoList);
}
