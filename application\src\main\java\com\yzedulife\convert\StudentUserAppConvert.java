package com.yzedulife.convert;

import com.yzedulife.response.StudentUserResponse;
import com.yzedulife.service.dto.StudentUserDTO;
import com.yzedulife.vo.StudentUserVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 学生用户应用层转换器
 */
@Mapper
public interface StudentUserAppConvert {
    StudentUserAppConvert INSTANCE = Mappers.getMapper(StudentUserAppConvert.class);

    /**
     * StudentUserVO转StudentUserDTO
     */
    StudentUserDTO vo2dto(StudentUserVO vo);

    /**
     * StudentUserDTO转StudentUserResponse
     */
    StudentUserResponse dto2response(StudentUserDTO dto);

    /**
     * StudentUserDTO列表转StudentUserResponse列表
     */
    List<StudentUserResponse> dto2responseList(List<StudentUserDTO> dtoList);
}
