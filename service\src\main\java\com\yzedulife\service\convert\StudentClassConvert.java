package com.yzedulife.service.convert;

import com.yzedulife.service.dto.StudentClassDTO;
import com.yzedulife.service.entity.StudentClass;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 学生班级转换器
 */
@Mapper
public interface StudentClassConvert {
    StudentClassConvert INSTANCE = Mappers.getMapper(StudentClassConvert.class);

    StudentClassDTO entity2dto(StudentClass studentClass);
    StudentClass dto2entity(StudentClassDTO studentClassDTO);

    List<StudentClassDTO> entity2dtoBatch(List<StudentClass> entities);
    List<StudentClass> dto2entityBatch(List<StudentClassDTO> dtos);
}
