package com.yzedulife.service.service.impl;

import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.common.domain.CommonErrorCode;
import com.yzedulife.service.dto.CodeDTO;
import com.yzedulife.service.service.CodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 验证码服务实现类
 * 使用内存存储验证码，支持过期时间
 */
@Slf4j
@Service
public class CodeServiceImpl implements CodeService {

    /**
     * 验证码存储容器
     */
    private final ConcurrentHashMap<String, CodeDTO> codeStorage = new ConcurrentHashMap<>();

    @Override
    public CodeDTO create(String phone, String code, Long expireSeconds) throws BusinessException {
        if (!StringUtils.hasText(phone)) {
            throw new BusinessException("手机号不能为空");
        }
        if (!StringUtils.hasText(code)) {
            throw new BusinessException("验证码不能为空");
        }
        if (expireSeconds == null || expireSeconds <= 0) {
            throw new BusinessException("过期时间必须大于0");
        }

        // 检查是否已存在未过期的验证码
        CodeDTO existingCode = codeStorage.get(phone);
        if (existingCode != null && existingCode.getExpiry().isAfter(LocalDateTime.now())) {
            throw new BusinessException(CommonErrorCode.E_500001);
        }

        // 创建新的验证码
        LocalDateTime expiry = LocalDateTime.now().plusSeconds(expireSeconds);
        CodeDTO codeDTO = new CodeDTO(phone, code, expiry);
        codeStorage.put(phone, codeDTO);

        log.info("创建验证码成功，手机号：{}，验证码：{}，过期时间：{}", phone, code, expiry);
        return codeDTO;
    }

    @Override
    public CodeDTO query(String phone) throws BusinessException {
        if (!StringUtils.hasText(phone)) {
            throw new BusinessException("手机号不能为空");
        }

        CodeDTO codeDTO = codeStorage.get(phone);
        if (codeDTO == null) {
            return null;
        }

        // 检查是否过期
        if (codeDTO.getExpiry().isBefore(LocalDateTime.now())) {
            codeStorage.remove(phone);
            return null;
        }

        return codeDTO;
    }

    @Override
    public Boolean delete(String phone) throws BusinessException {
        if (!StringUtils.hasText(phone)) {
            throw new BusinessException("手机号不能为空");
        }

        CodeDTO removed = codeStorage.remove(phone);
        return removed != null;
    }

    @Override
    public Boolean validate(String phone, String code) throws BusinessException {
        if (!StringUtils.hasText(phone)) {
            throw new BusinessException("手机号不能为空");
        }
        if (!StringUtils.hasText(code)) {
            throw new BusinessException("验证码不能为空");
        }

        CodeDTO codeDTO = query(phone);
        if (codeDTO == null) {
            return false;
        }

        return code.equals(codeDTO.getCode());
    }
}
