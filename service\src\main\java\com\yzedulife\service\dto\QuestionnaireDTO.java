package com.yzedulife.service.dto;

import com.yzedulife.service.convert.QuestionnaireConvert;
import com.yzedulife.service.entity.Questionnaire;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 问卷DTO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QuestionnaireDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 问卷ID
     */
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 描述（图片路径）
     */
    private String description;

    /**
     * 目标受众
     */
    private String targetAudience;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建者ID
     */
    private Long creatorId;

    public Questionnaire toEntity() {
        return QuestionnaireConvert.INSTANCE.dto2entity(this);
    }
}
