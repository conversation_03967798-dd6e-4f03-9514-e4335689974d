package com.yzedulife.service.convert;

import com.yzedulife.service.dto.OtherUserDTO;
import com.yzedulife.service.entity.OtherUser;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 社会人士用户转换器
 */
@Mapper
public interface OtherUserConvert {
    OtherUserConvert INSTANCE = Mappers.getMapper(OtherUserConvert.class);

    OtherUserDTO entity2dto(OtherUser otherUser);
    OtherUser dto2entity(OtherUserDTO otherUserDTO);

    List<OtherUserDTO> entity2dtoBatch(List<OtherUser> entities);
    List<OtherUser> dto2entityBatch(List<OtherUserDTO> dtos);
}
