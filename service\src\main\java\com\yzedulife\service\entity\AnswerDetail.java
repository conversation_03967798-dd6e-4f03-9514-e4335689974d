package com.yzedulife.service.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yzedulife.service.convert.AnswerDetailConvert;
import com.yzedulife.service.dto.AnswerDetailDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 答案详情实体类
 */
@Data
@TableName("answer_details")
public class AnswerDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 答案详情ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 所属答卷ID
     */
    @TableField("answer_sheet_id")
    private Long answerSheetId;

    /**
     * 题目ID
     */
    @TableField("question_id")
    private Long questionId;

    /**
     * 所选选项代号
     */
    @TableField("chosen_option_code")
    private String chosenOptionCode;

    /**
     * 是否正确 (nullable)
     */
    @TableField("is_correct")
    private Boolean isCorrect;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField("updated_time")
    private LocalDateTime updatedTime;

    public AnswerDetailDTO toDTO() {
        return AnswerDetailConvert.INSTANCE.entity2dto(this);
    }
}
