package com.yzedulife.service;

import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.common.domain.CommonErrorCode;
import com.yzedulife.service.dto.CodeDTO;
import com.yzedulife.service.service.impl.CodeServiceImpl;
import com.yzedulife.util.TestDataFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("验证码服务测试")
class CodeServiceTest {

    @InjectMocks
    private CodeServiceImpl codeService;

    private ConcurrentHashMap<String, CodeDTO> codeStorage;

    @BeforeEach
    void setUp() {
        codeStorage = new ConcurrentHashMap<>();
        ReflectionTestUtils.setField(codeService, "codeStorage", codeStorage);
    }

    @Test
    @DisplayName("创建验证码 - 成功")
    void create_Success() throws BusinessException {
        // Given
        String phone = "***********";
        String code = "123456";
        Long expireSeconds = 120L;

        // When
        CodeDTO result = codeService.create(phone, code, expireSeconds);

        // Then
        assertNotNull(result);
        assertEquals(phone, result.getPhone());
        assertEquals(code, result.getCode());
        assertTrue(result.getExpiry().isAfter(LocalDateTime.now()));
        assertTrue(result.getExpiry().isBefore(LocalDateTime.now().plusSeconds(121)));

        // 验证存储中是否有该验证码
        assertTrue(codeStorage.containsKey(phone));
        assertEquals(result, codeStorage.get(phone));
    }

    @Test
    @DisplayName("创建验证码 - 手机号为空")
    void create_EmptyPhone() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> codeService.create("", "123456", 120L));
        
        assertEquals("手机号不能为空", exception.getMessage());
        assertTrue(codeStorage.isEmpty());
    }

    @Test
    @DisplayName("创建验证码 - 手机号为null")
    void create_NullPhone() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> codeService.create(null, "123456", 120L));
        
        assertEquals("手机号不能为空", exception.getMessage());
        assertTrue(codeStorage.isEmpty());
    }

    @Test
    @DisplayName("创建验证码 - 验证码为空")
    void create_EmptyCode() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> codeService.create("***********", "", 120L));
        
        assertEquals("验证码不能为空", exception.getMessage());
        assertTrue(codeStorage.isEmpty());
    }

    @Test
    @DisplayName("创建验证码 - 验证码为null")
    void create_NullCode() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> codeService.create("***********", null, 120L));
        
        assertEquals("验证码不能为空", exception.getMessage());
        assertTrue(codeStorage.isEmpty());
    }

    @Test
    @DisplayName("创建验证码 - 过期时间为null")
    void create_NullExpireSeconds() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> codeService.create("***********", "123456", null));
        
        assertEquals("过期时间必须大于0", exception.getMessage());
        assertTrue(codeStorage.isEmpty());
    }

    @Test
    @DisplayName("创建验证码 - 过期时间为0")
    void create_ZeroExpireSeconds() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> codeService.create("***********", "123456", 0L));
        
        assertEquals("过期时间必须大于0", exception.getMessage());
        assertTrue(codeStorage.isEmpty());
    }

    @Test
    @DisplayName("创建验证码 - 过期时间为负数")
    void create_NegativeExpireSeconds() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> codeService.create("***********", "123456", -1L));
        
        assertEquals("过期时间必须大于0", exception.getMessage());
        assertTrue(codeStorage.isEmpty());
    }

    @Test
    @DisplayName("创建验证码 - 已存在未过期的验证码")
    void create_ExistingValidCode() throws BusinessException {
        // Given
        String phone = "***********";
        CodeDTO existingCode = TestDataFactory.createCodeDTO(phone, "111111", 
            LocalDateTime.now().plusMinutes(1));
        codeStorage.put(phone, existingCode);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> codeService.create(phone, "222222", 120L));
        
        assertEquals(CommonErrorCode.E_500001.getDesc(), exception.getMessage());
        
        // 验证原验证码仍然存在
        assertEquals(existingCode, codeStorage.get(phone));
    }

    @Test
    @DisplayName("创建验证码 - 覆盖已过期的验证码")
    void create_OverrideExpiredCode() throws BusinessException {
        // Given
        String phone = "***********";
        CodeDTO expiredCode = TestDataFactory.createExpiredCodeDTO();
        codeStorage.put(phone, expiredCode);

        // When
        CodeDTO result = codeService.create(phone, "123456", 120L);

        // Then
        assertNotNull(result);
        assertEquals(phone, result.getPhone());
        assertEquals("123456", result.getCode());
        assertTrue(result.getExpiry().isAfter(LocalDateTime.now()));

        // 验证新验证码已替换旧的
        assertEquals(result, codeStorage.get(phone));
        assertNotEquals(expiredCode, codeStorage.get(phone));
    }

    @Test
    @DisplayName("查询验证码 - 成功")
    void query_Success() throws BusinessException {
        // Given
        String phone = "***********";
        CodeDTO testCode = TestDataFactory.createCodeDTO();
        codeStorage.put(phone, testCode);

        // When
        CodeDTO result = codeService.query(phone);

        // Then
        assertNotNull(result);
        assertEquals(testCode, result);
    }

    @Test
    @DisplayName("查询验证码 - 手机号为空")
    void query_EmptyPhone() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> codeService.query(""));
        
        assertEquals("手机号不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("查询验证码 - 手机号为null")
    void query_NullPhone() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> codeService.query(null));
        
        assertEquals("手机号不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("查询验证码 - 验证码不存在")
    void query_CodeNotFound() throws BusinessException {
        // When
        CodeDTO result = codeService.query("***********");

        // Then
        assertNull(result);
    }

    @Test
    @DisplayName("查询验证码 - 验证码已过期")
    void query_CodeExpired() throws BusinessException {
        // Given
        String phone = "***********";
        CodeDTO expiredCode = TestDataFactory.createExpiredCodeDTO();
        codeStorage.put(phone, expiredCode);

        // When
        CodeDTO result = codeService.query(phone);

        // Then
        assertNull(result);
        // 验证过期的验证码已被删除
        assertFalse(codeStorage.containsKey(phone));
    }

    @Test
    @DisplayName("删除验证码 - 成功")
    void delete_Success() throws BusinessException {
        // Given
        String phone = "***********";
        CodeDTO testCode = TestDataFactory.createCodeDTO();
        codeStorage.put(phone, testCode);

        // When
        Boolean result = codeService.delete(phone);

        // Then
        assertTrue(result);
        assertFalse(codeStorage.containsKey(phone));
    }

    @Test
    @DisplayName("删除验证码 - 手机号为空")
    void delete_EmptyPhone() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> codeService.delete(""));
        
        assertEquals("手机号不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("删除验证码 - 验证码不存在")
    void delete_CodeNotFound() throws BusinessException {
        // When
        Boolean result = codeService.delete("***********");

        // Then
        assertFalse(result);
    }
}
