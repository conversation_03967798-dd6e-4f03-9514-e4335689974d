package com.yzedulife.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.QuestionDTO;
import com.yzedulife.service.dto.QuestionOptionDTO;
import com.yzedulife.service.service.QuestionService;
import com.yzedulife.service.service.QuestionOptionService;
import com.yzedulife.util.MockUtil;
import com.yzedulife.util.TestDataFactory;
import com.yzedulife.vo.QuestionVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(QuestionController.class)
@ActiveProfiles("test")
@DisplayName("题目控制器测试")
class QuestionControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private QuestionService questionService;

    @MockBean
    private QuestionOptionService questionOptionService;

    private QuestionDTO testQuestion;
    private QuestionVO testQuestionVO;
    private List<QuestionOptionDTO> testOptions;

    @BeforeEach
    void setUp() {
        testQuestion = TestDataFactory.createQuestionDTO();
        testQuestionVO = TestDataFactory.createQuestionVO();
        
        testOptions = new ArrayList<>();
        QuestionOptionDTO option1 = TestDataFactory.createQuestionOptionDTO();
        option1.setOptionCode("A");
        option1.setContent("选项A");
        testOptions.add(option1);
        
        QuestionOptionDTO option2 = TestDataFactory.createQuestionOptionDTO();
        option2.setId(2L);
        option2.setOptionCode("B");
        option2.setContent("选项B");
        testOptions.add(option2);
    }

    @Test
    @DisplayName("创建题目 - 成功")
    void create_Success() throws Exception {
        // Given
        when(questionService.getCountByPageId(1L)).thenReturn(0);
        when(questionService.create(any(QuestionDTO.class))).thenReturn(testQuestion);
        when(questionOptionService.create(any(QuestionOptionDTO.class)))
                .thenReturn(testOptions.get(0))
                .thenReturn(testOptions.get(1));
        when(questionOptionService.getByQuestionId(1L)).thenReturn(testOptions);

        // When & Then
        mockMvc.perform(MockUtil.postWithAdminToken("/question/create")
                        .content(MockUtil.asJsonString(testQuestionVO)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.content").value("测试题目"))
                .andExpect(jsonPath("$.data.options").isArray())
                .andExpect(jsonPath("$.data.options.length()").value(2));

        verify(questionService).getCountByPageId(1L);
        verify(questionService).create(any(QuestionDTO.class));
        verify(questionOptionService, times(2)).create(any(QuestionOptionDTO.class));
        verify(questionOptionService).getByQuestionId(1L);
    }

    @Test
    @DisplayName("创建题目 - 业务异常")
    void create_BusinessException() throws Exception {
        // Given
        when(questionService.getCountByPageId(1L)).thenReturn(0);
        when(questionService.create(any(QuestionDTO.class)))
                .thenThrow(new BusinessException("题目内容不能为空"));

        // When & Then
        mockMvc.perform(MockUtil.postWithAdminToken("/question/create")
                        .content(MockUtil.asJsonString(testQuestionVO)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.msg").value("题目内容不能为空"));

        verify(questionService).getCountByPageId(1L);
        verify(questionService).create(any(QuestionDTO.class));
        verify(questionOptionService, never()).create(any());
    }

    @Test
    @DisplayName("创建题目 - 无权限")
    void create_NoPermission() throws Exception {
        // When & Then
        mockMvc.perform(MockUtil.postWithStudentToken("/question/create")
                        .content(MockUtil.asJsonString(testQuestionVO)))
                .andDo(print())
                .andExpect(status().isForbidden());

        verify(questionService, never()).create(any());
    }

    @Test
    @DisplayName("修改题目 - 成功")
    void update_Success() throws Exception {
        // Given
        testQuestionVO.setId(1L);
        when(questionService.update(any(QuestionDTO.class))).thenReturn(testQuestion);
        doNothing().when(questionOptionService).deleteByQuestionId(1L);
        when(questionOptionService.create(any(QuestionOptionDTO.class)))
                .thenReturn(testOptions.get(0))
                .thenReturn(testOptions.get(1));
        when(questionOptionService.generateOptionCode(1L))
                .thenReturn("A")
                .thenReturn("B");
        when(questionOptionService.getByQuestionId(1L)).thenReturn(testOptions);

        // When & Then
        mockMvc.perform(MockUtil.postWithAdminToken("/question/update")
                        .content(MockUtil.asJsonString(testQuestionVO)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.options").isArray());

        verify(questionService).update(any(QuestionDTO.class));
        verify(questionOptionService).deleteByQuestionId(1L);
        verify(questionOptionService, times(2)).create(any(QuestionOptionDTO.class));
        verify(questionOptionService).getByQuestionId(1L);
    }

    @Test
    @DisplayName("修改题目 - ID为空")
    void update_NullId() throws Exception {
        // Given
        testQuestionVO.setId(null);

        // When & Then
        mockMvc.perform(MockUtil.postWithAdminToken("/question/update")
                        .content(MockUtil.asJsonString(testQuestionVO)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.msg").value("题目ID不能为空"));

        verify(questionService, never()).update(any());
        verify(questionOptionService, never()).deleteByQuestionId(any());
    }

    @Test
    @DisplayName("修改题目 - 题目不存在")
    void update_QuestionNotFound() throws Exception {
        // Given
        testQuestionVO.setId(999L);
        when(questionService.update(any(QuestionDTO.class)))
                .thenThrow(new BusinessException("题目不存在"));

        // When & Then
        mockMvc.perform(MockUtil.postWithAdminToken("/question/update")
                        .content(MockUtil.asJsonString(testQuestionVO)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.msg").value("题目不存在"));

        verify(questionService).update(any(QuestionDTO.class));
        verify(questionOptionService).deleteByQuestionId(999L);
        verify(questionOptionService, never()).create(any());
    }

    @Test
    @DisplayName("删除题目 - 成功")
    void delete_Success() throws Exception {
        // Given
        doNothing().when(questionOptionService).deleteByQuestionId(1L);
        when(questionService.deleteById(1L)).thenReturn(true);

        // When & Then
        mockMvc.perform(MockUtil.postWithAdminToken("/question/delete")
                        .param("id", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.msg").value("删除成功"));

        verify(questionOptionService).deleteByQuestionId(1L);
        verify(questionService).deleteById(1L);
    }

    @Test
    @DisplayName("删除题目 - 删除失败")
    void delete_Failed() throws Exception {
        // Given
        doNothing().when(questionOptionService).deleteByQuestionId(1L);
        when(questionService.deleteById(1L)).thenReturn(false);

        // When & Then
        mockMvc.perform(MockUtil.postWithAdminToken("/question/delete")
                        .param("id", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.msg").value("删除失败"));

        verify(questionOptionService).deleteByQuestionId(1L);
        verify(questionService).deleteById(1L);
    }

    @Test
    @DisplayName("删除题目 - 业务异常")
    void delete_BusinessException() throws Exception {
        // Given
        doThrow(new BusinessException("题目不存在"))
                .when(questionOptionService).deleteByQuestionId(1L);

        // When & Then
        mockMvc.perform(MockUtil.postWithAdminToken("/question/delete")
                        .param("id", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.msg").value("题目不存在"));

        verify(questionOptionService).deleteByQuestionId(1L);
        verify(questionService, never()).deleteById(any());
    }

    @Test
    @DisplayName("删除题目 - 无权限")
    void delete_NoPermission() throws Exception {
        // When & Then
        mockMvc.perform(MockUtil.postWithStudentToken("/question/delete")
                        .param("id", "1"))
                .andDo(print())
                .andExpect(status().isForbidden());

        verify(questionOptionService, never()).deleteByQuestionId(any());
        verify(questionService, never()).deleteById(any());
    }

    @Test
    @DisplayName("删除题目 - 系统异常")
    void delete_SystemException() throws Exception {
        // Given
        doThrow(new RuntimeException("系统错误"))
                .when(questionOptionService).deleteByQuestionId(1L);

        // When & Then
        mockMvc.perform(MockUtil.postWithAdminToken("/question/delete")
                        .param("id", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.msg").value("删除题目失败"));

        verify(questionOptionService).deleteByQuestionId(1L);
        verify(questionService, never()).deleteById(any());
    }
}
