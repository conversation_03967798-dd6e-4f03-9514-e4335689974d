package com.yzedulife.interceptor;

import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.common.domain.CommonErrorCode;
import com.yzedulife.common.domain.ErrorCode;
import com.yzedulife.common.domain.RestErrorResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@ControllerAdvice
public class GlobalExceptionHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 处理参数类型转换异常
     */
    @ExceptionHandler(value = MethodArgumentTypeMismatchException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public RestErrorResponse processMethodArgumentTypeMismatchException(HttpServletRequest request, HttpServletResponse response, MethodArgumentTypeMismatchException e) {
        response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*");
        LOGGER.warn("参数类型转换异常: 参数名={}, 传入值={}, 期望类型={}",
                   e.getName(), e.getValue(), e.getRequiredType().getSimpleName());

        String message = String.format("参数 '%s' 的值 '%s' 无效，期望类型为 %s",
                                     e.getName(), e.getValue(), e.getRequiredType().getSimpleName());
        return new RestErrorResponse(CommonErrorCode.E_100003.getCode(), message);
    }

    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public RestErrorResponse processException(HttpServletRequest request, HttpServletResponse response, Exception e) {
        response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*");
        if (e instanceof BusinessException) {
            LOGGER.info(e.getMessage(), e);
            BusinessException businessException = (BusinessException) e;
            ErrorCode errorCode = ((BusinessException) e).getErrorCode();
            return new RestErrorResponse(errorCode.getCode(), errorCode.getDesc());
        }
        LOGGER.error("系统异常:", e);
        return new RestErrorResponse(CommonErrorCode.UNKNOWN.getCode(), CommonErrorCode.UNKNOWN.getDesc());
    }


}
