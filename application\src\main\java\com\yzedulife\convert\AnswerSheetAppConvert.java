package com.yzedulife.convert;

import com.yzedulife.response.AnswerSheetResponse;
import com.yzedulife.service.dto.AnswerSheetDTO;
import com.yzedulife.vo.AnswerSheetVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 答卷应用层转换器
 */
@Mapper
public interface AnswerSheetAppConvert {
    AnswerSheetAppConvert INSTANCE = Mappers.getMapper(AnswerSheetAppConvert.class);

    /**
     * AnswerSheetVO转DTO
     */
    AnswerSheetDTO vo2dto(AnswerSheetVO vo);

    /**
     * AnswerSheetDTO转Response
     */
    AnswerSheetResponse dto2response(AnswerSheetDTO dto);

    /**
     * AnswerSheetDTO列表转Response列表
     */
    List<AnswerSheetResponse> dto2responseList(List<AnswerSheetDTO> dtoList);
}
