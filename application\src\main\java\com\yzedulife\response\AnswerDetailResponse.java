package com.yzedulife.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 答案详情响应
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "答案详情响应")
public class AnswerDetailResponse {
    
    @Schema(description = "题目ID")
    private Long questionId;
    
    @Schema(description = "所选选项代号")
    private String chosenOptionCode;
}
