package com.yzedulife.service.convert;

import com.yzedulife.service.dto.QuestionnairePageDTO;
import com.yzedulife.service.entity.QuestionnairePage;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 问卷页面转换器
 */
@Mapper
public interface QuestionnairePageConvert {
    QuestionnairePageConvert INSTANCE = Mappers.getMapper(QuestionnairePageConvert.class);

    QuestionnairePageDTO entity2dto(QuestionnairePage questionnairePage);
    QuestionnairePage dto2entity(QuestionnairePageDTO questionnairePageDTO);

    List<QuestionnairePageDTO> entity2dtoBatch(List<QuestionnairePage> entities);
    List<QuestionnairePage> dto2entityBatch(List<QuestionnairePageDTO> dtos);
}
