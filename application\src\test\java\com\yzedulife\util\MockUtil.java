package com.yzedulife.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yzedulife.common.util.JwtUtil;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;

/**
 * Mock工具类
 * 用于简化测试中的Mock操作
 */
public class MockUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 创建带有管理员Token的GET请求
     */
    public static MockHttpServletRequestBuilder getWithAdminToken(String url) {
        String token = JwtUtil.createToken("admin", "1");
        return get(url).header("Authorization", "Bearer " + token);
    }

    /**
     * 创建带有学生Token的GET请求
     */
    public static MockHttpServletRequestBuilder getWithStudentToken(String url) {
        String token = JwtUtil.createToken("student", "1");
        return get(url).header("Authorization", "Bearer " + token);
    }

    /**
     * 创建带有社会人士Token的GET请求
     */
    public static MockHttpServletRequestBuilder getWithOtherToken(String url) {
        String token = JwtUtil.createToken("other", "1");
        return get(url).header("Authorization", "Bearer " + token);
    }

    /**
     * 创建带有管理员Token的POST请求
     */
    public static MockHttpServletRequestBuilder postWithAdminToken(String url) {
        String token = JwtUtil.createToken("admin", "1");
        return post(url)
                .header("Authorization", "Bearer " + token)
                .contentType(MediaType.APPLICATION_JSON);
    }

    /**
     * 创建带有学生Token的POST请求
     */
    public static MockHttpServletRequestBuilder postWithStudentToken(String url) {
        String token = JwtUtil.createToken("student", "1");
        return post(url)
                .header("Authorization", "Bearer " + token)
                .contentType(MediaType.APPLICATION_JSON);
    }

    /**
     * 创建带有社会人士Token的POST请求
     */
    public static MockHttpServletRequestBuilder postWithOtherToken(String url) {
        String token = JwtUtil.createToken("other", "1");
        return post(url)
                .header("Authorization", "Bearer " + token)
                .contentType(MediaType.APPLICATION_JSON);
    }

    /**
     * 创建带有自定义Token的GET请求
     */
    public static MockHttpServletRequestBuilder getWithToken(String url, String userType, String userId) {
        String token = JwtUtil.createToken(userType, userId);
        return get(url).header("Authorization", "Bearer " + token);
    }

    /**
     * 创建带有自定义Token的POST请求
     */
    public static MockHttpServletRequestBuilder postWithToken(String url, String userType, String userId) {
        String token = JwtUtil.createToken(userType, userId);
        return post(url)
                .header("Authorization", "Bearer " + token)
                .contentType(MediaType.APPLICATION_JSON);
    }

    /**
     * 创建不带Token的GET请求
     */
    public static MockHttpServletRequestBuilder getWithoutToken(String url) {
        return get(url);
    }

    /**
     * 创建不带Token的POST请求
     */
    public static MockHttpServletRequestBuilder postWithoutToken(String url) {
        return post(url).contentType(MediaType.APPLICATION_JSON);
    }

    /**
     * 将对象转换为JSON字符串
     */
    public static String asJsonString(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert object to JSON", e);
        }
    }

    /**
     * 创建带有表单参数的POST请求（不带Token）
     */
    public static MockHttpServletRequestBuilder postFormWithoutToken(String url) {
        return post(url).contentType(MediaType.APPLICATION_FORM_URLENCODED);
    }

    /**
     * 创建带有表单参数的POST请求（带管理员Token）
     */
    public static MockHttpServletRequestBuilder postFormWithAdminToken(String url) {
        String token = JwtUtil.createToken("admin", "1");
        return post(url)
                .header("Authorization", "Bearer " + token)
                .contentType(MediaType.APPLICATION_FORM_URLENCODED);
    }

    /**
     * 创建带有表单参数的POST请求（带学生Token）
     */
    public static MockHttpServletRequestBuilder postFormWithStudentToken(String url) {
        String token = JwtUtil.createToken("student", "1");
        return post(url)
                .header("Authorization", "Bearer " + token)
                .contentType(MediaType.APPLICATION_FORM_URLENCODED);
    }

    /**
     * 创建带有表单参数的POST请求（带社会人士Token）
     */
    public static MockHttpServletRequestBuilder postFormWithOtherToken(String url) {
        String token = JwtUtil.createToken("other", "1");
        return post(url)
                .header("Authorization", "Bearer " + token)
                .contentType(MediaType.APPLICATION_FORM_URLENCODED);
    }

    /**
     * 创建PUT请求（带管理员Token）
     */
    public static MockHttpServletRequestBuilder putWithAdminToken(String url) {
        String token = JwtUtil.createToken("admin", "1");
        return put(url)
                .header("Authorization", "Bearer " + token)
                .contentType(MediaType.APPLICATION_JSON);
    }

    /**
     * 创建DELETE请求（带管理员Token）
     */
    public static MockHttpServletRequestBuilder deleteWithAdminToken(String url) {
        String token = JwtUtil.createToken("admin", "1");
        return delete(url).header("Authorization", "Bearer " + token);
    }
}
