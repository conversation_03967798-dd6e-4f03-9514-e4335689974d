package com.yzedulife;

import org.junit.platform.suite.api.IncludeClassNamePatterns;
import org.junit.platform.suite.api.SelectPackages;
import org.junit.platform.suite.api.Suite;
import org.junit.platform.suite.api.SuiteDisplayName;

/**
 * 测试套件 - 运行所有测试
 */
@Suite
@SuiteDisplayName("YZJudge API 测试套件")
@SelectPackages({
    "com.yzedulife.controller",
    "com.yzedulife.service", 
    "com.yzedulife.util",
    "com.yzedulife.integration"
})
@IncludeClassNamePatterns(".*Test")
public class TestSuite {
    // 测试套件类，用于运行所有测试
}
