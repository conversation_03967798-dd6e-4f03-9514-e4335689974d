package com.yzedulife.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.QuestionDTO;
import com.yzedulife.service.entity.Question;
import com.yzedulife.service.entity.QuestionOption;
import com.yzedulife.service.mapper.QuestionMapper;
import com.yzedulife.service.mapper.QuestionOptionMapper;
import com.yzedulife.service.service.QuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 题目服务实现类
 */
@Service
@Transactional
public class QuestionServiceImpl implements QuestionService {

    @Autowired
    private QuestionMapper questionMapper;
    
    @Autowired
    private QuestionOptionMapper questionOptionMapper;

    @Override
    public QuestionDTO getById(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("题目ID不能为空");
        }
        Question question = questionMapper.selectById(id);
        if (question == null) {
            throw new BusinessException("题目不存在");
        }
        return question.toDTO();
    }

    @Override
    public QuestionDTO create(QuestionDTO questionDTO) throws BusinessException {
        if (questionDTO == null) {
            throw new BusinessException("题目信息不能为空");
        }
        if (questionDTO.getPageId() == null) {
            throw new BusinessException("页面ID不能为空");
        }
        if (!StringUtils.hasText(questionDTO.getContent())) {
            throw new BusinessException("题目内容不能为空");
        }
        if (questionDTO.getDisplayOrder() == null) {
            throw new BusinessException("显示顺序不能为空");
        }
        
        Question question = questionDTO.toEntity();
        question.setId(null); // 确保是新增
        int result = questionMapper.insert(question);
        if (result <= 0) {
            throw new BusinessException("创建题目失败");
        }
        return question.toDTO();
    }

    @Override
    public QuestionDTO update(QuestionDTO questionDTO) throws BusinessException {
        if (questionDTO == null || questionDTO.getId() == null) {
            throw new BusinessException("题目ID不能为空");
        }
        
        // 检查题目是否存在
        Question existingQuestion = questionMapper.selectById(questionDTO.getId());
        if (existingQuestion == null) {
            throw new BusinessException("题目不存在");
        }
        
        Question question = questionDTO.toEntity();
        int result = questionMapper.updateById(question);
        if (result <= 0) {
            throw new BusinessException("更新题目失败");
        }
        return questionMapper.selectById(question.getId()).toDTO();
    }

    @Override
    public Boolean deleteById(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("题目ID不能为空");
        }
        
        Question question = questionMapper.selectById(id);
        if (question == null) {
            throw new BusinessException("题目不存在");
        }
        
        // 删除题目相关的选项
        questionOptionMapper.delete(new LambdaQueryWrapper<QuestionOption>()
                .eq(QuestionOption::getQuestionId, id));
        
        int result = questionMapper.deleteById(id);
        return result > 0;
    }

    @Override
    public List<QuestionDTO> getByPageId(Long pageId) throws BusinessException {
        if (pageId == null) {
            throw new BusinessException("页面ID不能为空");
        }
        List<Question> questions = questionMapper.selectList(new LambdaQueryWrapper<Question>()
                .eq(Question::getPageId, pageId)
                .orderByAsc(Question::getDisplayOrder));
        return questions.stream().map(Question::toDTO).collect(java.util.stream.Collectors.toList());
    }

    @Override
    public Integer getCountByPageId(Long pageId) throws BusinessException {
        if (pageId == null) {
            throw new BusinessException("页面ID不能为空");
        }
        return Math.toIntExact(questionMapper.selectCount(new LambdaQueryWrapper<Question>()
                .eq(Question::getPageId, pageId)));
    }

    @Override
    public Boolean adjustDisplayOrder(Long pageId, List<Long> questionIds) throws BusinessException {
        if (pageId == null) {
            throw new BusinessException("页面ID不能为空");
        }
        if (questionIds == null || questionIds.isEmpty()) {
            throw new BusinessException("题目ID列表不能为空");
        }
        
        // 验证所有题目都属于该页面
        for (Long questionId : questionIds) {
            Question question = questionMapper.selectById(questionId);
            if (question == null || !question.getPageId().equals(pageId)) {
                throw new BusinessException("题目不属于该页面");
            }
        }
        
        // 重新设置显示顺序
        for (int i = 0; i < questionIds.size(); i++) {
            Question question = new Question();
            question.setId(questionIds.get(i));
            question.setDisplayOrder(i + 1);
            questionMapper.updateById(question);
        }
        
        return true;
    }

    @Override
    public Boolean setCorrectAnswer(Long questionId, String optionCode) throws BusinessException {
        if (questionId == null) {
            throw new BusinessException("题目ID不能为空");
        }

        Question question = questionMapper.selectById(questionId);
        if (question == null) {
            throw new BusinessException("题目不存在");
        }

        // 如果设置了选项代号，验证选项是否属于该题目
        if (optionCode != null && !optionCode.trim().isEmpty()) {
            QuestionOption option = questionOptionMapper.selectOne(new LambdaQueryWrapper<QuestionOption>()
                    .eq(QuestionOption::getQuestionId, questionId)
                    .eq(QuestionOption::getOptionCode, optionCode));
            if (option == null) {
                throw new BusinessException("选项代号不属于该题目");
            }
        }

        question.setCorrectAnswerCode(optionCode);
        int result = questionMapper.updateById(question);
        return result > 0;
    }

    @Override
    public Boolean batchDelete(List<Long> questionIds) throws BusinessException {
        if (questionIds == null || questionIds.isEmpty()) {
            throw new BusinessException("题目ID列表不能为空");
        }
        
        for (Long questionId : questionIds) {
            deleteById(questionId);
        }
        
        return true;
    }

    @Override
    public QuestionDTO copyToPage(Long questionId, Long targetPageId) throws BusinessException {
        if (questionId == null) {
            throw new BusinessException("题目ID不能为空");
        }
        if (targetPageId == null) {
            throw new BusinessException("目标页面ID不能为空");
        }
        
        Question originalQuestion = questionMapper.selectById(questionId);
        if (originalQuestion == null) {
            throw new BusinessException("原题目不存在");
        }
        
        // 获取目标页面的最大显示顺序
        Integer maxOrder = questionMapper.selectList(new LambdaQueryWrapper<Question>()
                .eq(Question::getPageId, targetPageId)
                .orderByDesc(Question::getDisplayOrder)
                .last("LIMIT 1"))
                .stream()
                .findFirst()
                .map(Question::getDisplayOrder)
                .orElse(0);
        
        // 创建题目副本
        Question copyQuestion = new Question();
        copyQuestion.setPageId(targetPageId);
        copyQuestion.setContentType(originalQuestion.getContentType());
        copyQuestion.setContent(originalQuestion.getContent());
        copyQuestion.setDisplayOrder(maxOrder + 1);
        copyQuestion.setCorrectAnswerCode(null); // 复制时不复制正确答案
        
        int result = questionMapper.insert(copyQuestion);
        if (result <= 0) {
            throw new BusinessException("复制题目失败");
        }
        
        // 复制选项
        List<QuestionOption> originalOptions = questionOptionMapper.selectList(new LambdaQueryWrapper<QuestionOption>()
                .eq(QuestionOption::getQuestionId, questionId));
        
        for (QuestionOption originalOption : originalOptions) {
            QuestionOption copyOption = new QuestionOption();
            copyOption.setQuestionId(copyQuestion.getId());
            copyOption.setOptionType(originalOption.getOptionType());
            copyOption.setContent(originalOption.getContent());
            copyOption.setOptionCode(originalOption.getOptionCode());
            questionOptionMapper.insert(copyOption);
        }
        
        return copyQuestion.toDTO();
    }
}
