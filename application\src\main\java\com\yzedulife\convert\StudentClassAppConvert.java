package com.yzedulife.convert;

import com.yzedulife.response.StudentClassResponse;
import com.yzedulife.service.dto.StudentClassDTO;
import com.yzedulife.vo.StudentClassVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 学生班级应用层转换器
 */
@Mapper
public interface StudentClassAppConvert {
    StudentClassAppConvert INSTANCE = Mappers.getMapper(StudentClassAppConvert.class);

    /**
     * StudentClassVO转StudentClassDTO
     */
    StudentClassDTO vo2dto(StudentClassVO vo);

    /**
     * StudentClassDTO转StudentClassResponse
     */
    StudentClassResponse dto2response(StudentClassDTO dto);

    /**
     * StudentClassDTO列表转StudentClassResponse列表
     */
    List<StudentClassResponse> dto2responseList(List<StudentClassDTO> dtoList);
}
