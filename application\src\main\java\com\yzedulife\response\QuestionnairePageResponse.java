package com.yzedulife.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 问卷页面响应
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "问卷页面响应")
public class QuestionnairePageResponse {
    
    @Schema(description = "页面ID")
    private Long id;
    
    @Schema(description = "页码")
    private Integer pageNumber;
    
    @Schema(description = "题目列表")
    private List<QuestionResponse> questions;
}
