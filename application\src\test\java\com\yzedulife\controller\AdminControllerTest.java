package com.yzedulife.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yzedulife.service.service.AdminService;
import com.yzedulife.util.MockUtil;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(AdminController.class)
@ActiveProfiles("test")
@DisplayName("管理员控制器测试")
class AdminControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private AdminService adminService;

    @Test
    @DisplayName("验证管理员身份 - 成功")
    void verifyAdmin_Success() throws Exception {
        // When & Then
        mockMvc.perform(MockUtil.getWithAdminToken("/verifyAdmin"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.msg").value("管理员身份验证成功"));
    }

    @Test
    @DisplayName("验证管理员身份 - 无Token")
    void verifyAdmin_NoToken() throws Exception {
        // When & Then
        mockMvc.perform(MockUtil.getWithoutToken("/verifyAdmin"))
                .andDo(print())
                .andExpect(status().isForbidden());
    }

    @Test
    @DisplayName("验证管理员身份 - 学生Token无权限")
    void verifyAdmin_StudentTokenNoPermission() throws Exception {
        // When & Then
        mockMvc.perform(MockUtil.getWithStudentToken("/verifyAdmin"))
                .andDo(print())
                .andExpect(status().isForbidden());
    }

    @Test
    @DisplayName("验证管理员身份 - 社会人士Token无权限")
    void verifyAdmin_OtherTokenNoPermission() throws Exception {
        // When & Then
        mockMvc.perform(MockUtil.getWithOtherToken("/verifyAdmin"))
                .andDo(print())
                .andExpect(status().isForbidden());
    }

    @Test
    @DisplayName("验证管理员身份 - 无效Token")
    void verifyAdmin_InvalidToken() throws Exception {
        // When & Then
        mockMvc.perform(MockUtil.getWithoutToken("/verifyAdmin")
                        .header("Authorization", "Bearer invalid-token"))
                .andDo(print())
                .andExpect(status().isForbidden());
    }

    @Test
    @DisplayName("验证管理员身份 - Token格式错误")
    void verifyAdmin_MalformedToken() throws Exception {
        // When & Then
        mockMvc.perform(MockUtil.getWithoutToken("/verifyAdmin")
                        .header("Authorization", "invalid-format"))
                .andDo(print())
                .andExpect(status().isForbidden());
    }

    @Test
    @DisplayName("验证管理员身份 - 空Authorization头")
    void verifyAdmin_EmptyAuthorizationHeader() throws Exception {
        // When & Then
        mockMvc.perform(MockUtil.getWithoutToken("/verifyAdmin")
                        .header("Authorization", ""))
                .andDo(print())
                .andExpect(status().isForbidden());
    }
}
