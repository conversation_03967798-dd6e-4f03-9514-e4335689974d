package com.yzedulife.service.convert;

import com.yzedulife.service.dto.StudentUserDTO;
import com.yzedulife.service.entity.StudentUser;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 学生用户转换器
 */
@Mapper
public interface StudentUserConvert {
    StudentUserConvert INSTANCE = Mappers.getMapper(StudentUserConvert.class);

    StudentUserDTO entity2dto(StudentUser studentUser);
    StudentUser dto2entity(StudentUserDTO studentUserDTO);

    List<StudentUserDTO> entity2dtoBatch(List<StudentUser> entities);
    List<StudentUser> dto2entityBatch(List<StudentUserDTO> dtos);
}
