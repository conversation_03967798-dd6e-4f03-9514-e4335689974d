package com.yzedulife.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.QuestionnaireDTO;
import com.yzedulife.service.entity.Questionnaire;
import com.yzedulife.service.mapper.QuestionnaireMapper;
import com.yzedulife.service.service.impl.QuestionnaireServiceImpl;
import com.yzedulife.util.TestDataFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("问卷服务测试")
class QuestionnaireServiceTest {

    @Mock
    private QuestionnaireMapper questionnaireMapper;

    @InjectMocks
    private QuestionnaireServiceImpl questionnaireService;

    private Questionnaire testQuestionnaire;
    private QuestionnaireDTO testQuestionnaireDTO;
    private List<Questionnaire> testQuestionnaireList;

    @BeforeEach
    void setUp() {
        testQuestionnaireDTO = TestDataFactory.createQuestionnaireDTO();
        testQuestionnaire = new Questionnaire();
        testQuestionnaire.setId(testQuestionnaireDTO.getId());
        testQuestionnaire.setTitle(testQuestionnaireDTO.getTitle());
        testQuestionnaire.setTargetAudience(testQuestionnaireDTO.getTargetAudience());
        testQuestionnaire.setStatus(testQuestionnaireDTO.getStatus());
        testQuestionnaire.setCreatorId(testQuestionnaireDTO.getCreatorId());

        testQuestionnaireList = new ArrayList<>();
        testQuestionnaireList.add(testQuestionnaire);
        
        Questionnaire questionnaire2 = new Questionnaire();
        questionnaire2.setId(2L);
        questionnaire2.setTitle("测试问卷2");
        questionnaire2.setTargetAudience("SOCIAL");
        questionnaire2.setStatus(1);
        questionnaire2.setCreatorId(1L);
        testQuestionnaireList.add(questionnaire2);
    }

    @Test
    @DisplayName("根据ID获取问卷 - 成功")
    void getById_Success() throws BusinessException {
        // Given
        when(questionnaireMapper.selectById(1L)).thenReturn(testQuestionnaire);

        // When
        QuestionnaireDTO result = questionnaireService.getById(1L);

        // Then
        assertNotNull(result);
        assertEquals(testQuestionnaireDTO.getId(), result.getId());
        assertEquals(testQuestionnaireDTO.getTitle(), result.getTitle());
        assertEquals(testQuestionnaireDTO.getTargetAudience(), result.getTargetAudience());
        assertEquals(testQuestionnaireDTO.getStatus(), result.getStatus());
        assertEquals(testQuestionnaireDTO.getCreatorId(), result.getCreatorId());

        verify(questionnaireMapper).selectById(1L);
    }

    @Test
    @DisplayName("根据ID获取问卷 - ID为空")
    void getById_NullId() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> questionnaireService.getById(null));
        
        assertEquals("问卷ID不能为空", exception.getMessage());
        verify(questionnaireMapper, never()).selectById(any());
    }

    @Test
    @DisplayName("根据ID获取问卷 - 问卷不存在")
    void getById_QuestionnaireNotFound() {
        // Given
        when(questionnaireMapper.selectById(999L)).thenReturn(null);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> questionnaireService.getById(999L));
        
        assertEquals("问卷不存在", exception.getMessage());
        verify(questionnaireMapper).selectById(999L);
    }

    @Test
    @DisplayName("创建问卷 - 成功")
    void create_Success() throws BusinessException {
        // Given
        when(questionnaireMapper.insert(any(Questionnaire.class))).thenReturn(1);

        // When
        QuestionnaireDTO result = questionnaireService.create(testQuestionnaireDTO);

        // Then
        assertNotNull(result);
        assertEquals(testQuestionnaireDTO.getTitle(), result.getTitle());
        assertEquals(testQuestionnaireDTO.getTargetAudience(), result.getTargetAudience());
        assertEquals(testQuestionnaireDTO.getStatus(), result.getStatus());
        assertEquals(testQuestionnaireDTO.getCreatorId(), result.getCreatorId());

        verify(questionnaireMapper).insert(any(Questionnaire.class));
    }

    @Test
    @DisplayName("创建问卷 - 问卷信息为空")
    void create_NullQuestionnaire() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> questionnaireService.create(null));
        
        assertEquals("问卷信息不能为空", exception.getMessage());
        verify(questionnaireMapper, never()).insert(any(Questionnaire.class));
    }

    @Test
    @DisplayName("创建问卷 - 标题为空")
    void create_EmptyTitle() {
        // Given
        testQuestionnaireDTO.setTitle("");

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> questionnaireService.create(testQuestionnaireDTO));
        
        assertEquals("问卷标题不能为空", exception.getMessage());
        verify(questionnaireMapper, never()).insert(any(Questionnaire.class));
    }

    @Test
    @DisplayName("创建问卷 - 目标受众为空")
    void create_EmptyTargetAudience() {
        // Given
        testQuestionnaireDTO.setTargetAudience("");

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> questionnaireService.create(testQuestionnaireDTO));
        
        assertEquals("目标受众不能为空", exception.getMessage());
        verify(questionnaireMapper, never()).insert(any(Questionnaire.class));
    }

    @Test
    @DisplayName("创建问卷 - 创建者ID为空")
    void create_NullCreatorId() {
        // Given
        testQuestionnaireDTO.setCreatorId(null);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> questionnaireService.create(testQuestionnaireDTO));
        
        assertEquals("创建者ID不能为空", exception.getMessage());
        verify(questionnaireMapper, never()).insert(any(Questionnaire.class));
    }

    @Test
    @DisplayName("创建问卷 - 数据库插入失败")
    void create_InsertFailed() {
        // Given
        when(questionnaireMapper.insert(any(Questionnaire.class))).thenReturn(0);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> questionnaireService.create(testQuestionnaireDTO));
        
        assertEquals("创建问卷失败", exception.getMessage());
        verify(questionnaireMapper).insert(any(Questionnaire.class));
    }

    @Test
    @DisplayName("更新问卷 - 成功")
    void update_Success() throws BusinessException {
        // Given
        when(questionnaireMapper.selectById(1L)).thenReturn(testQuestionnaire);
        when(questionnaireMapper.updateById(any(Questionnaire.class))).thenReturn(1);

        // When
        QuestionnaireDTO result = questionnaireService.update(testQuestionnaireDTO);

        // Then
        assertNotNull(result);
        assertEquals(testQuestionnaireDTO.getId(), result.getId());
        assertEquals(testQuestionnaireDTO.getTitle(), result.getTitle());

        verify(questionnaireMapper).selectById(1L);
        verify(questionnaireMapper).updateById(any(Questionnaire.class));
    }

    @Test
    @DisplayName("更新问卷 - 问卷信息为空")
    void update_NullQuestionnaire() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> questionnaireService.update(null));
        
        assertEquals("问卷信息不能为空", exception.getMessage());
        verify(questionnaireMapper, never()).selectById(any());
        verify(questionnaireMapper, never()).updateById(any(Questionnaire.class));
    }

    @Test
    @DisplayName("更新问卷 - ID为空")
    void update_NullId() {
        // Given
        testQuestionnaireDTO.setId(null);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> questionnaireService.update(testQuestionnaireDTO));
        
        assertEquals("问卷ID不能为空", exception.getMessage());
        verify(questionnaireMapper, never()).selectById(any());
        verify(questionnaireMapper, never()).updateById(any(Questionnaire.class));
    }

    @Test
    @DisplayName("更新问卷 - 问卷不存在")
    void update_QuestionnaireNotFound() {
        // Given
        when(questionnaireMapper.selectById(1L)).thenReturn(null);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> questionnaireService.update(testQuestionnaireDTO));
        
        assertEquals("问卷不存在", exception.getMessage());
        verify(questionnaireMapper).selectById(1L);
        verify(questionnaireMapper, never()).updateById(any(Questionnaire.class));
    }

    @Test
    @DisplayName("删除问卷 - 成功")
    void deleteById_Success() throws BusinessException {
        // Given
        when(questionnaireMapper.selectById(1L)).thenReturn(testQuestionnaire);
        when(questionnaireMapper.deleteById(1L)).thenReturn(1);

        // When
        Boolean result = questionnaireService.deleteById(1L);

        // Then
        assertTrue(result);
        verify(questionnaireMapper).selectById(1L);
        verify(questionnaireMapper).deleteById(1L);
    }

    @Test
    @DisplayName("删除问卷 - ID为空")
    void deleteById_NullId() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> questionnaireService.deleteById(null));
        
        assertEquals("问卷ID不能为空", exception.getMessage());
        verify(questionnaireMapper, never()).selectById(any());
        verify(questionnaireMapper, never()).deleteById(any());
    }

    @Test
    @DisplayName("删除问卷 - 问卷不存在")
    void deleteById_QuestionnaireNotFound() {
        // Given
        when(questionnaireMapper.selectById(1L)).thenReturn(null);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> questionnaireService.deleteById(1L));
        
        assertEquals("问卷不存在", exception.getMessage());
        verify(questionnaireMapper).selectById(1L);
        verify(questionnaireMapper, never()).deleteById(any());
    }

    @Test
    @DisplayName("获取所有问卷 - 成功")
    void getAll_Success() throws BusinessException {
        // Given
        when(questionnaireMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(testQuestionnaireList);

        // When
        List<QuestionnaireDTO> result = questionnaireService.getAll();

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("测试问卷", result.get(0).getTitle());
        assertEquals("测试问卷2", result.get(1).getTitle());

        verify(questionnaireMapper).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("获取所有问卷 - 空列表")
    void getAll_EmptyList() throws BusinessException {
        // Given
        when(questionnaireMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(new ArrayList<>());

        // When
        List<QuestionnaireDTO> result = questionnaireService.getAll();

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(questionnaireMapper).selectList(any(LambdaQueryWrapper.class));
    }
}
