package com.yzedulife.service.dto;

import com.yzedulife.service.convert.StudentUserConvert;
import com.yzedulife.service.entity.StudentUser;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 学生用户DTO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class StudentUserDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 学生ID
     */
    private Long id;

    /**
     * 姓名
     */
    private String name;

    /**
     * 学号
     */
    private String studentNumber;

    /**
     * 班级ID
     */
    private Long classId;

    public StudentUser toEntity() {
        return StudentUserConvert.INSTANCE.dto2entity(this);
    }
}
