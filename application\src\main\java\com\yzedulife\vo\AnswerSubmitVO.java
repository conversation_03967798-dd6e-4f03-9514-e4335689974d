package com.yzedulife.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 答案提交VO（用于提交答卷时，不包含答卷ID）
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "答案提交VO")
public class AnswerSubmitVO {
    
    @Schema(description = "题目ID", required = true)
    @NotNull(message = "题目ID不能为空")
    private Long questionId;
    
    @Schema(description = "所选选项代号", required = true)
    @NotNull(message = "所选选项代号不能为空")
    private String chosenOptionCode;
}
