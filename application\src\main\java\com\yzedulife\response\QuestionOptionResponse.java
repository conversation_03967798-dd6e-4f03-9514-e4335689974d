package com.yzedulife.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 题目选项响应
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "题目选项响应")
public class QuestionOptionResponse {
    
    @Schema(description = "选项ID")
    private Long id;
    
    @Schema(description = "选项类型", example = "TEXT 或 IMAGE")
    private String optionType;
    
    @Schema(description = "选项内容")
    private String content;
    
    @Schema(description = "选项代号", example = "A, B, C, D")
    private String optionCode;
}
