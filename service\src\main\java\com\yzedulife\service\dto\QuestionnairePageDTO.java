package com.yzedulife.service.dto;

import com.yzedulife.service.convert.QuestionnairePageConvert;
import com.yzedulife.service.entity.QuestionnairePage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 问卷页面DTO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QuestionnairePageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 页面ID
     */
    private Long id;

    /**
     * 所属问卷ID
     */
    private Long questionnaireId;

    /**
     * 页码
     */
    private Integer pageNumber;

    public QuestionnairePage toEntity() {
        return QuestionnairePageConvert.INSTANCE.dto2entity(this);
    }
}
