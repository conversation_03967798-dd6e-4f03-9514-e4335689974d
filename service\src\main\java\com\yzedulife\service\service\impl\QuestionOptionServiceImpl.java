package com.yzedulife.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.QuestionOptionDTO;
import com.yzedulife.service.entity.QuestionOption;
import com.yzedulife.service.mapper.QuestionOptionMapper;
import com.yzedulife.service.service.QuestionOptionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 题目选项服务实现类
 */
@Service
@Transactional
public class QuestionOptionServiceImpl implements QuestionOptionService {

    @Autowired
    private QuestionOptionMapper questionOptionMapper;

    @Override
    public QuestionOptionDTO getById(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("题目选项ID不能为空");
        }
        QuestionOption questionOption = questionOptionMapper.selectById(id);
        if (questionOption == null) {
            throw new BusinessException("题目选项不存在");
        }
        return questionOption.toDTO();
    }

    @Override
    public QuestionOptionDTO create(QuestionOptionDTO questionOptionDTO) throws BusinessException {
        if (questionOptionDTO == null) {
            throw new BusinessException("题目选项信息不能为空");
        }
        if (questionOptionDTO.getQuestionId() == null) {
            throw new BusinessException("题目ID不能为空");
        }
        if (!StringUtils.hasText(questionOptionDTO.getOptionType())) {
            throw new BusinessException("选项类型不能为空");
        }
        if (!StringUtils.hasText(questionOptionDTO.getContent())) {
            throw new BusinessException("选项内容不能为空");
        }
        if (!StringUtils.hasText(questionOptionDTO.getOptionCode())) {
            throw new BusinessException("选项代号不能为空");
        }
        
        // 检查选项代号是否已存在
        if (isOptionCodeExist(questionOptionDTO.getQuestionId(), questionOptionDTO.getOptionCode())) {
            throw new BusinessException("选项代号已存在");
        }
        
        QuestionOption questionOption = questionOptionDTO.toEntity();
        questionOption.setId(null); // 确保是新增
        int result = questionOptionMapper.insert(questionOption);
        if (result <= 0) {
            throw new BusinessException("创建题目选项失败");
        }
        return questionOption.toDTO();
    }

    @Override
    public QuestionOptionDTO update(QuestionOptionDTO questionOptionDTO) throws BusinessException {
        if (questionOptionDTO == null || questionOptionDTO.getId() == null) {
            throw new BusinessException("题目选项ID不能为空");
        }
        
        // 检查选项是否存在
        QuestionOption existingOption = questionOptionMapper.selectById(questionOptionDTO.getId());
        if (existingOption == null) {
            throw new BusinessException("题目选项不存在");
        }
        
        // 如果修改了选项代号，检查新代号是否已存在
        if (StringUtils.hasText(questionOptionDTO.getOptionCode()) && 
            !questionOptionDTO.getOptionCode().equals(existingOption.getOptionCode())) {
            if (isOptionCodeExist(existingOption.getQuestionId(), questionOptionDTO.getOptionCode())) {
                throw new BusinessException("选项代号已存在");
            }
        }
        
        QuestionOption questionOption = questionOptionDTO.toEntity();
        int result = questionOptionMapper.updateById(questionOption);
        if (result <= 0) {
            throw new BusinessException("更新题目选项失败");
        }
        return questionOptionMapper.selectById(questionOption.getId()).toDTO();
    }

    @Override
    public Boolean deleteById(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("题目选项ID不能为空");
        }
        
        QuestionOption questionOption = questionOptionMapper.selectById(id);
        if (questionOption == null) {
            throw new BusinessException("题目选项不存在");
        }
        
        int result = questionOptionMapper.deleteById(id);
        return result > 0;
    }

    @Override
    public List<QuestionOptionDTO> getByQuestionId(Long questionId) throws BusinessException {
        if (questionId == null) {
            throw new BusinessException("题目ID不能为空");
        }
        List<QuestionOption> options = questionOptionMapper.selectList(new LambdaQueryWrapper<QuestionOption>()
                .eq(QuestionOption::getQuestionId, questionId)
                .orderByAsc(QuestionOption::getOptionCode));
        return options.stream().map(QuestionOption::toDTO).collect(java.util.stream.Collectors.toList());
    }

    @Override
    public Integer getCountByQuestionId(Long questionId) throws BusinessException {
        if (questionId == null) {
            throw new BusinessException("题目ID不能为空");
        }
        return Math.toIntExact(questionOptionMapper.selectCount(new LambdaQueryWrapper<QuestionOption>()
                .eq(QuestionOption::getQuestionId, questionId)));
    }

    @Override
    public QuestionOptionDTO getByQuestionIdAndOptionCode(Long questionId, String optionCode) throws BusinessException {
        if (questionId == null) {
            throw new BusinessException("题目ID不能为空");
        }
        if (!StringUtils.hasText(optionCode)) {
            throw new BusinessException("选项代号不能为空");
        }
        
        QuestionOption questionOption = questionOptionMapper.selectOne(new LambdaQueryWrapper<QuestionOption>()
                .eq(QuestionOption::getQuestionId, questionId)
                .eq(QuestionOption::getOptionCode, optionCode));
        
        if (questionOption == null) {
            throw new BusinessException("题目选项不存在");
        }
        
        return questionOption.toDTO();
    }

    @Override
    public Boolean batchCreate(List<QuestionOptionDTO> questionOptionDTOs) throws BusinessException {
        if (questionOptionDTOs == null || questionOptionDTOs.isEmpty()) {
            throw new BusinessException("题目选项列表不能为空");
        }
        
        for (QuestionOptionDTO dto : questionOptionDTOs) {
            create(dto);
        }
        
        return true;
    }

    @Override
    public Boolean batchDelete(List<Long> optionIds) throws BusinessException {
        if (optionIds == null || optionIds.isEmpty()) {
            throw new BusinessException("选项ID列表不能为空");
        }
        
        for (Long optionId : optionIds) {
            deleteById(optionId);
        }
        
        return true;
    }

    @Override
    public Boolean deleteByQuestionId(Long questionId) throws BusinessException {
        if (questionId == null) {
            throw new BusinessException("题目ID不能为空");
        }
        
        int result = questionOptionMapper.delete(new LambdaQueryWrapper<QuestionOption>()
                .eq(QuestionOption::getQuestionId, questionId));
        
        return result >= 0; // 即使没有选项也返回true
    }

    @Override
    public Boolean isOptionCodeExist(Long questionId, String optionCode) throws BusinessException {
        if (questionId == null || !StringUtils.hasText(optionCode)) {
            return false;
        }
        return questionOptionMapper.exists(new LambdaQueryWrapper<QuestionOption>()
                .eq(QuestionOption::getQuestionId, questionId)
                .eq(QuestionOption::getOptionCode, optionCode));
    }

    @Override
    public String generateOptionCode(Long questionId) throws BusinessException {
        if (questionId == null) {
            throw new BusinessException("题目ID不能为空");
        }
        
        // 获取当前题目的选项数量
        Integer count = getCountByQuestionId(questionId);
        
        // 生成选项代号 A, B, C, D...
        char optionChar = (char) ('A' + count);
        String optionCode = String.valueOf(optionChar);
        
        // 如果超过Z，使用AA, AB, AC...
        if (count >= 26) {
            int firstChar = (count - 26) / 26;
            int secondChar = (count - 26) % 26;
            optionCode = String.valueOf((char) ('A' + firstChar)) + String.valueOf((char) ('A' + secondChar));
        }
        
        return optionCode;
    }
}
