package com.yzedulife.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.QuestionnairePageDTO;
import com.yzedulife.service.entity.QuestionnairePage;
import com.yzedulife.service.mapper.QuestionnairePageMapper;
import com.yzedulife.service.service.QuestionnairePageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 问卷页面服务实现类
 */
@Service
@Transactional
public class QuestionnairePageServiceImpl implements QuestionnairePageService {

    @Autowired
    private QuestionnairePageMapper questionnairePageMapper;

    @Override
    public QuestionnairePageDTO getById(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("问卷页面ID不能为空");
        }
        QuestionnairePage questionnairePage = questionnairePageMapper.selectById(id);
        if (questionnairePage == null) {
            throw new BusinessException("问卷页面不存在");
        }
        return questionnairePage.toDTO();
    }

    @Override
    public QuestionnairePageDTO create(QuestionnairePageDTO questionnairePageDTO) throws BusinessException {
        if (questionnairePageDTO == null) {
            throw new BusinessException("问卷页面信息不能为空");
        }
        if (questionnairePageDTO.getQuestionnaireId() == null) {
            throw new BusinessException("问卷ID不能为空");
        }
        if (questionnairePageDTO.getPageNumber() == null) {
            throw new BusinessException("页码不能为空");
        }
        
        // 检查同一问卷下页码是否已存在
        QuestionnairePage existingPage = questionnairePageMapper.selectOne(new LambdaQueryWrapper<QuestionnairePage>()
                .eq(QuestionnairePage::getQuestionnaireId, questionnairePageDTO.getQuestionnaireId())
                .eq(QuestionnairePage::getPageNumber, questionnairePageDTO.getPageNumber()));
        if (existingPage != null) {
            throw new BusinessException("该页码已存在");
        }
        
        QuestionnairePage questionnairePage = questionnairePageDTO.toEntity();
        questionnairePage.setId(null); // 确保是新增
        int result = questionnairePageMapper.insert(questionnairePage);
        if (result <= 0) {
            throw new BusinessException("创建问卷页面失败");
        }
        return questionnairePage.toDTO();
    }

    @Override
    public QuestionnairePageDTO update(QuestionnairePageDTO questionnairePageDTO) throws BusinessException {
        if (questionnairePageDTO == null || questionnairePageDTO.getId() == null) {
            throw new BusinessException("问卷页面ID不能为空");
        }
        
        // 检查页面是否存在
        QuestionnairePage existingPage = questionnairePageMapper.selectById(questionnairePageDTO.getId());
        if (existingPage == null) {
            throw new BusinessException("问卷页面不存在");
        }
        
        // 如果修改了页码，检查新页码是否已存在
        if (questionnairePageDTO.getPageNumber() != null && 
            !questionnairePageDTO.getPageNumber().equals(existingPage.getPageNumber())) {
            QuestionnairePage duplicatePage = questionnairePageMapper.selectOne(new LambdaQueryWrapper<QuestionnairePage>()
                    .eq(QuestionnairePage::getQuestionnaireId, existingPage.getQuestionnaireId())
                    .eq(QuestionnairePage::getPageNumber, questionnairePageDTO.getPageNumber()));
            if (duplicatePage != null) {
                throw new BusinessException("该页码已存在");
            }
        }
        
        QuestionnairePage questionnairePage = questionnairePageDTO.toEntity();
        int result = questionnairePageMapper.updateById(questionnairePage);
        if (result <= 0) {
            throw new BusinessException("更新问卷页面失败");
        }
        return questionnairePageMapper.selectById(questionnairePage.getId()).toDTO();
    }

    @Override
    public Boolean deleteById(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("问卷页面ID不能为空");
        }
        
        QuestionnairePage questionnairePage = questionnairePageMapper.selectById(id);
        if (questionnairePage == null) {
            throw new BusinessException("问卷页面不存在");
        }
        
        int result = questionnairePageMapper.deleteById(id);
        return result > 0;
    }

    @Override
    public List<QuestionnairePageDTO> getByQuestionnaireId(Long questionnaireId) throws BusinessException {
        if (questionnaireId == null) {
            throw new BusinessException("问卷ID不能为空");
        }
        List<QuestionnairePage> pages = questionnairePageMapper.selectList(new LambdaQueryWrapper<QuestionnairePage>()
                .eq(QuestionnairePage::getQuestionnaireId, questionnaireId)
                .orderByAsc(QuestionnairePage::getPageNumber));
        return pages.stream().map(QuestionnairePage::toDTO).collect(java.util.stream.Collectors.toList());
    }

    @Override
    public QuestionnairePageDTO getByQuestionnaireIdAndPageNumber(Long questionnaireId, Integer pageNumber) throws BusinessException {
        if (questionnaireId == null) {
            throw new BusinessException("问卷ID不能为空");
        }
        if (pageNumber == null) {
            throw new BusinessException("页码不能为空");
        }
        
        QuestionnairePage questionnairePage = questionnairePageMapper.selectOne(new LambdaQueryWrapper<QuestionnairePage>()
                .eq(QuestionnairePage::getQuestionnaireId, questionnaireId)
                .eq(QuestionnairePage::getPageNumber, pageNumber));
        
        if (questionnairePage == null) {
            throw new BusinessException("问卷页面不存在");
        }
        
        return questionnairePage.toDTO();
    }

    @Override
    public Integer getPageCountByQuestionnaireId(Long questionnaireId) throws BusinessException {
        if (questionnaireId == null) {
            throw new BusinessException("问卷ID不能为空");
        }
        return Math.toIntExact(questionnairePageMapper.selectCount(new LambdaQueryWrapper<QuestionnairePage>()
                .eq(QuestionnairePage::getQuestionnaireId, questionnaireId)));
    }

    @Override
    public Boolean adjustPageOrder(Long questionnaireId, List<Long> pageIds) throws BusinessException {
        if (questionnaireId == null) {
            throw new BusinessException("问卷ID不能为空");
        }
        if (pageIds == null || pageIds.isEmpty()) {
            throw new BusinessException("页面ID列表不能为空");
        }
        
        // 验证所有页面都属于该问卷
        for (Long pageId : pageIds) {
            QuestionnairePage page = questionnairePageMapper.selectById(pageId);
            if (page == null || !page.getQuestionnaireId().equals(questionnaireId)) {
                throw new BusinessException("页面不属于该问卷");
            }
        }
        
        // 重新设置页码
        for (int i = 0; i < pageIds.size(); i++) {
            QuestionnairePage page = new QuestionnairePage();
            page.setId(pageIds.get(i));
            page.setPageNumber(i + 1);
            questionnairePageMapper.updateById(page);
        }
        
        return true;
    }
}
