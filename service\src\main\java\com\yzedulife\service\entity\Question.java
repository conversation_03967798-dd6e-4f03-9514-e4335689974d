package com.yzedulife.service.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yzedulife.service.convert.QuestionConvert;
import com.yzedulife.service.dto.QuestionDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 题目实体类
 */
@Data
@TableName("questions")
public class Question implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 题目ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 所属页面ID
     */
    @TableField("page_id")
    private Long pageId;

    /**
     * 题干类型
     */
    @TableField("content_type")
    private String contentType;

    /**
     * 题干内容
     */
    @TableField("content")
    private String content;

    /**
     * 显示顺序
     */
    @TableField("display_order")
    private Integer displayOrder;

    /**
     * 正确答案选项代号 (nullable)
     */
    @TableField("correct_answer_code")
    private String correctAnswerCode;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField("updated_time")
    private LocalDateTime updatedTime;

    public QuestionDTO toDTO() {
        return QuestionConvert.INSTANCE.entity2dto(this);
    }
}
