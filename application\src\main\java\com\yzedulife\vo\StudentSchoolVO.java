package com.yzedulife.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 学生学校VO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "学生学校VO")
public class StudentSchoolVO implements Serializable {

    @Schema(description = "学校ID")
    private Long id;

    @Schema(description = "学校名称", required = true)
    @NotBlank(message = "学校名称不能为空")
    private String schoolName;
}
