package com.yzedulife.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.StudentUserDTO;
import com.yzedulife.service.entity.StudentUser;
import com.yzedulife.service.entity.StudentClass;
import com.yzedulife.service.mapper.StudentUserMapper;
import com.yzedulife.service.mapper.StudentClassMapper;
import com.yzedulife.service.service.StudentUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 学生用户服务实现类
 */
@Service
@Transactional
public class StudentUserServiceImpl implements StudentUserService {

    @Autowired
    private StudentUserMapper studentUserMapper;

    @Autowired
    private StudentClassMapper studentClassMapper;

    @Override
    public StudentUserDTO getById(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("学生用户ID不能为空");
        }
        StudentUser studentUser = studentUserMapper.selectById(id);
        if (studentUser == null) {
            throw new BusinessException("学生用户不存在");
        }
        return studentUser.toDTO();
    }

    @Override
    public StudentUserDTO getByStudentNumber(String studentNumber) throws BusinessException {
        if (!StringUtils.hasText(studentNumber)) {
            throw new BusinessException("学号不能为空");
        }
        StudentUser studentUser = studentUserMapper.selectOne(new LambdaQueryWrapper<StudentUser>()
                .eq(StudentUser::getStudentNumber, studentNumber));
        if (studentUser == null) {
            throw new BusinessException("学生用户不存在");
        }
        return studentUser.toDTO();
    }

    @Override
    public StudentUserDTO create(StudentUserDTO studentUserDTO) throws BusinessException {
        if (studentUserDTO == null) {
            throw new BusinessException("学生用户信息不能为空");
        }
        if (!StringUtils.hasText(studentUserDTO.getName())) {
            throw new BusinessException("姓名不能为空");
        }
        if (!StringUtils.hasText(studentUserDTO.getStudentNumber())) {
            throw new BusinessException("学号不能为空");
        }
        if (studentUserDTO.getClassId() == null) {
            throw new BusinessException("班级ID不能为空");
        }
        
        StudentUser studentUser = studentUserDTO.toEntity();
        studentUser.setId(null); // 确保是新增
        int result = studentUserMapper.insert(studentUser);
        if (result <= 0) {
            throw new BusinessException("创建学生用户失败");
        }
        return studentUser.toDTO();
    }

    @Override
    public StudentUserDTO update(StudentUserDTO studentUserDTO) throws BusinessException {
        if (studentUserDTO == null || studentUserDTO.getId() == null) {
            throw new BusinessException("学生用户ID不能为空");
        }
        
        // 检查学生用户是否存在
        StudentUser existingUser = studentUserMapper.selectById(studentUserDTO.getId());
        if (existingUser == null) {
            throw new BusinessException("学生用户不存在");
        }
        
        // 验证必填字段
        if (StringUtils.hasText(studentUserDTO.getName())) {
            existingUser.setName(studentUserDTO.getName());
        }
        if (StringUtils.hasText(studentUserDTO.getStudentNumber())) {
            existingUser.setStudentNumber(studentUserDTO.getStudentNumber());
        }
        if (studentUserDTO.getClassId() != null) {
            existingUser.setClassId(studentUserDTO.getClassId());
        }
        
        int result = studentUserMapper.updateById(existingUser);
        if (result <= 0) {
            throw new BusinessException("更新学生用户失败");
        }
        return existingUser.toDTO();
    }

    @Override
    public Boolean deleteById(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("学生用户ID不能为空");
        }
        
        StudentUser studentUser = studentUserMapper.selectById(id);
        if (studentUser == null) {
            throw new BusinessException("学生用户不存在");
        }
        
        int result = studentUserMapper.deleteById(id);
        return result > 0;
    }

    @Override
    public List<StudentUserDTO> getAll() throws BusinessException {
        List<StudentUser> studentUsers = studentUserMapper.selectList(new LambdaQueryWrapper<StudentUser>()
                .orderByDesc(StudentUser::getId));
        return studentUsers.stream().map(StudentUser::toDTO).collect(java.util.stream.Collectors.toList());
    }

    @Override
    public Boolean isExist(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("学生用户ID不能为空");
        }
        return studentUserMapper.exists(new LambdaQueryWrapper<StudentUser>()
                .eq(StudentUser::getId, id));
    }

    @Override
    public Boolean isStudentNumberExist(String studentNumber) throws BusinessException {
        if (!StringUtils.hasText(studentNumber)) {
            return false;
        }
        return studentUserMapper.exists(new LambdaQueryWrapper<StudentUser>()
                .eq(StudentUser::getStudentNumber, studentNumber));
    }

    @Override
    public Boolean batchImport(List<StudentUserDTO> studentUsers) throws BusinessException {
        if (studentUsers == null || studentUsers.isEmpty()) {
            throw new BusinessException("导入数据不能为空");
        }

        // 验证必填字段
        for (StudentUserDTO dto : studentUsers) {
            if (!StringUtils.hasText(dto.getName())) {
                throw new BusinessException("姓名不能为空");
            }
            if (!StringUtils.hasText(dto.getStudentNumber())) {
                throw new BusinessException("学号不能为空");
            }
            if (dto.getClassId() == null) {
                throw new BusinessException("班级ID不能为空");
            }
        }

        for (StudentUserDTO dto : studentUsers) {
            create(dto);
        }

        return true;
    }

    @Override
    public List<StudentUserDTO> getByClassId(Long classId) throws BusinessException {
        if (classId == null) {
            throw new BusinessException("班级ID不能为空");
        }
        List<StudentUser> studentUsers = studentUserMapper.selectList(new LambdaQueryWrapper<StudentUser>()
                .eq(StudentUser::getClassId, classId)
                .orderByDesc(StudentUser::getId));
        return studentUsers.stream().map(StudentUser::toDTO).collect(java.util.stream.Collectors.toList());
    }

    @Override
    public List<StudentUserDTO> getBySchoolId(Long schoolId) throws BusinessException {
        if (schoolId == null) {
            throw new BusinessException("学校ID不能为空");
        }
        // 先获取该学校的所有班级ID
        List<StudentClass> classes = studentClassMapper.selectList(new LambdaQueryWrapper<StudentClass>()
                .eq(StudentClass::getSchoolId, schoolId));

        if (classes.isEmpty()) {
            return new java.util.ArrayList<>();
        }

        List<Long> classIds = classes.stream().map(StudentClass::getId).collect(java.util.stream.Collectors.toList());

        // 根据班级ID列表获取所有学生
        List<StudentUser> studentUsers = studentUserMapper.selectList(new LambdaQueryWrapper<StudentUser>()
                .in(StudentUser::getClassId, classIds)
                .orderByDesc(StudentUser::getId));
        return studentUsers.stream().map(StudentUser::toDTO).collect(java.util.stream.Collectors.toList());
    }
}
