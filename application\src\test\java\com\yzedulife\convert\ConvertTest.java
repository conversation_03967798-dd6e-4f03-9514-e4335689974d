package com.yzedulife.convert;

import com.yzedulife.service.dto.AnswerDetailDTO;
import com.yzedulife.vo.AnswerDetailVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
public class ConvertTest {

    @Test
    public void testAnswerDetailAppConvert() {
        // 测试应用层转换器
        AnswerDetailVO vo = new AnswerDetailVO();
        vo.setQuestionId(1L);
        vo.setChosenOptionCode("A");
        
        AnswerDetailDTO dto = AnswerDetailAppConvert.INSTANCE.vo2dto(vo);
        
        assertNotNull(dto);
        assertEquals(1L, dto.getQuestionId());
        assertEquals("A", dto.getChosenOptionCode());
    }
}
