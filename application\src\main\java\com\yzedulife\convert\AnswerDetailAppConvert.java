package com.yzedulife.convert;

import com.yzedulife.response.AnswerDetailResponse;
import com.yzedulife.service.dto.AnswerDetailDTO;
import com.yzedulife.vo.AnswerDetailVO;
import com.yzedulife.vo.AnswerSubmitVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 答案详情应用层转换器
 */
@Mapper
public interface AnswerDetailAppConvert {
    AnswerDetailAppConvert INSTANCE = Mappers.getMapper(AnswerDetailAppConvert.class);

    /**
     * AnswerDetailVO转DTO
     */
    AnswerDetailDTO vo2dto(AnswerDetailVO vo);

    /**
     * AnswerSubmitVO转DTO（用于提交答卷）
     */
    AnswerDetailDTO submitVo2dto(AnswerSubmitVO vo);

    /**
     * AnswerDetailDTO转Response
     */
    AnswerDetailResponse dto2response(AnswerDetailDTO dto);
}
