package com.yzedulife.service.convert;

import com.yzedulife.service.dto.QuestionnaireDTO;
import com.yzedulife.service.entity.Questionnaire;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 问卷转换器
 */
@Mapper
public interface QuestionnaireConvert {
    QuestionnaireConvert INSTANCE = Mappers.getMapper(QuestionnaireConvert.class);

    QuestionnaireDTO entity2dto(Questionnaire questionnaire);
    Questionnaire dto2entity(QuestionnaireDTO questionnaireDTO);

    List<QuestionnaireDTO> entity2dtoBatch(List<Questionnaire> entities);
    List<Questionnaire> dto2entityBatch(List<QuestionnaireDTO> dtos);
}
