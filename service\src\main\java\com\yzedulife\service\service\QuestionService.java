package com.yzedulife.service.service;

import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.QuestionDTO;

import java.util.List;

/**
 * 题目服务接口
 */
public interface QuestionService {

    /**
     * 根据ID获取题目
     */
    QuestionDTO getById(Long id) throws BusinessException;

    /**
     * 创建题目
     */
    QuestionDTO create(QuestionDTO questionDTO) throws BusinessException;

    /**
     * 更新题目
     */
    QuestionDTO update(QuestionDTO questionDTO) throws BusinessException;

    /**
     * 删除题目
     */
    Boolean deleteById(Long id) throws BusinessException;

    /**
     * 根据页面ID获取所有题目
     */
    List<QuestionDTO> getByPageId(Long pageId) throws BusinessException;

    /**
     * 根据页面ID获取题目数量
     */
    Integer getCountByPageId(Long pageId) throws BusinessException;

    /**
     * 调整题目显示顺序
     */
    Boolean adjustDisplayOrder(Long pageId, List<Long> questionIds) throws BusinessException;

    /**
     * 设置正确答案
     */
    Boolean setCorrectAnswer(Long questionId, String optionCode) throws BusinessException;

    /**
     * 批量删除题目
     */
    Boolean batchDelete(List<Long> questionIds) throws BusinessException;

    /**
     * 复制题目到其他页面
     */
    QuestionDTO copyToPage(Long questionId, Long targetPageId) throws BusinessException;
}
