package com.yzedulife.service.service;

import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.AdminDTO;

import java.util.List;

/**
 * 管理员服务接口
 */
public interface AdminService {

    /**
     * 根据ID获取管理员
     */
    AdminDTO getById(Long id) throws BusinessException;

    /**
     * 根据用户名获取管理员
     */
    AdminDTO getByUsername(String username) throws BusinessException;

    /**
     * 创建管理员
     */
    AdminDTO create(AdminDTO adminDTO) throws BusinessException;

    /**
     * 更新管理员
     */
    AdminDTO update(AdminDTO adminDTO) throws BusinessException;

    /**
     * 删除管理员
     */
    Boolean deleteById(Long id) throws BusinessException;

    /**
     * 获取所有管理员
     */
    List<AdminDTO> getAll() throws BusinessException;

    /**
     * 检查用户是否存在
     */
    Boolean isExist(Long id) throws BusinessException;

    /**
     * 检查用户名是否存在
     */
    Boolean isUsernameExist(String username) throws BusinessException;

    /**
     * 验证管理员登录
     */
    AdminDTO validateLogin(String username, String password) throws BusinessException;
}
