package com.yzedulife.service.dto;

import com.yzedulife.service.convert.AnswerDetailConvert;
import com.yzedulife.service.entity.AnswerDetail;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 答案详情DTO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class AnswerDetailDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 答案详情ID
     */
    private Long id;

    /**
     * 所属答卷ID
     */
    private Long answerSheetId;

    /**
     * 题目ID
     */
    private Long questionId;

    /**
     * 所选选项代号
     */
    private String chosenOptionCode;

    /**
     * 是否正确 (nullable)
     */
    private Boolean isCorrect;

    public AnswerDetail toEntity() {
        return AnswerDetailConvert.INSTANCE.dto2entity(this);
    }
}
