package com.yzedulife.service.dto;

import com.yzedulife.service.convert.AnswerSheetConvert;
import com.yzedulife.service.entity.AnswerSheet;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 答卷DTO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class AnswerSheetDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 答卷ID
     */
    private Long id;

    /**
     * 所属问卷ID
     */
    private Long questionnaireId;

    /**
     * 提交者类型
     */
    private String submitterType;

    /**
     * 学生ID (nullable)
     */
    private Long studentUserId;

    /**
     * 社会人士ID (nullable)
     */
    private Long otherUserId;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    public AnswerSheet toEntity() {
        return AnswerSheetConvert.INSTANCE.dto2entity(this);
    }
}
