package com.yzedulife.service.service;

import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.QuestionOptionDTO;

import java.util.List;

/**
 * 题目选项服务接口
 */
public interface QuestionOptionService {

    /**
     * 根据ID获取题目选项
     */
    QuestionOptionDTO getById(Long id) throws BusinessException;

    /**
     * 创建题目选项
     */
    QuestionOptionDTO create(QuestionOptionDTO questionOptionDTO) throws BusinessException;

    /**
     * 更新题目选项
     */
    QuestionOptionDTO update(QuestionOptionDTO questionOptionDTO) throws BusinessException;

    /**
     * 删除题目选项
     */
    Boolean deleteById(Long id) throws BusinessException;

    /**
     * 根据题目ID获取所有选项
     */
    List<QuestionOptionDTO> getByQuestionId(Long questionId) throws BusinessException;

    /**
     * 根据题目ID获取选项数量
     */
    Integer getCountByQuestionId(Long questionId) throws BusinessException;

    /**
     * 根据选项代号获取选项
     */
    QuestionOptionDTO getByQuestionIdAndOptionCode(Long questionId, String optionCode) throws BusinessException;

    /**
     * 批量创建选项
     */
    Boolean batchCreate(List<QuestionOptionDTO> questionOptionDTOs) throws BusinessException;

    /**
     * 批量删除选项
     */
    Boolean batchDelete(List<Long> optionIds) throws BusinessException;

    /**
     * 删除题目的所有选项
     */
    Boolean deleteByQuestionId(Long questionId) throws BusinessException;

    /**
     * 检查选项代号是否存在
     */
    Boolean isOptionCodeExist(Long questionId, String optionCode) throws BusinessException;

    /**
     * 自动生成选项代号
     */
    String generateOptionCode(Long questionId) throws BusinessException;
}
