package com.yzedulife.service.convert;

import com.yzedulife.service.dto.QuestionOptionDTO;
import com.yzedulife.service.entity.QuestionOption;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 题目选项转换器
 */
@Mapper
public interface QuestionOptionConvert {
    QuestionOptionConvert INSTANCE = Mappers.getMapper(QuestionOptionConvert.class);

    QuestionOptionDTO entity2dto(QuestionOption questionOption);
    QuestionOption dto2entity(QuestionOptionDTO questionOptionDTO);

    List<QuestionOptionDTO> entity2dtoBatch(List<QuestionOption> entities);
    List<QuestionOption> dto2entityBatch(List<QuestionOptionDTO> dtos);
}
