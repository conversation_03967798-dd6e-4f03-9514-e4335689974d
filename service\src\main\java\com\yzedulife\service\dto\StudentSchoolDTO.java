package com.yzedulife.service.dto;

import com.yzedulife.service.convert.StudentSchoolConvert;
import com.yzedulife.service.entity.StudentSchool;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 学生学校DTO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class StudentSchoolDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 学校ID
     */
    private Long id;

    /**
     * 学校名称
     */
    private String schoolName;

    public StudentSchool toEntity() {
        return StudentSchoolConvert.INSTANCE.dto2entity(this);
    }
}
