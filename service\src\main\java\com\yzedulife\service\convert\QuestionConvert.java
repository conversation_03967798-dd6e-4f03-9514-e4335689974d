package com.yzedulife.service.convert;

import com.yzedulife.service.dto.QuestionDTO;
import com.yzedulife.service.entity.Question;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 题目转换器
 */
@Mapper
public interface QuestionConvert {
    QuestionConvert INSTANCE = Mappers.getMapper(QuestionConvert.class);

    QuestionDTO entity2dto(Question question);
    Question dto2entity(QuestionDTO questionDTO);

    List<QuestionDTO> entity2dtoBatch(List<Question> entities);
    List<Question> dto2entityBatch(List<QuestionDTO> dtos);
}
