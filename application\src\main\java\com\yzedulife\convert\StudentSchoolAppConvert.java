package com.yzedulife.convert;

import com.yzedulife.response.StudentSchoolResponse;
import com.yzedulife.service.dto.StudentSchoolDTO;
import com.yzedulife.vo.StudentSchoolVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 学生学校应用层转换器
 */
@Mapper
public interface StudentSchoolAppConvert {
    StudentSchoolAppConvert INSTANCE = Mappers.getMapper(StudentSchoolAppConvert.class);

    /**
     * StudentSchoolVO转StudentSchoolDTO
     */
    StudentSchoolDTO vo2dto(StudentSchoolVO vo);

    /**
     * StudentSchoolDTO转StudentSchoolResponse
     */
    StudentSchoolResponse dto2response(StudentSchoolDTO dto);

    /**
     * StudentSchoolDTO列表转StudentSchoolResponse列表
     */
    List<StudentSchoolResponse> dto2responseList(List<StudentSchoolDTO> dtoList);
}
