package com.yzedulife.service.convert;

import com.yzedulife.service.dto.AnswerDetailDTO;
import com.yzedulife.service.entity.AnswerDetail;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 答案详情转换器
 */
@Mapper
public interface AnswerDetailConvert {
    AnswerDetailConvert INSTANCE = Mappers.getMapper(AnswerDetailConvert.class);

    AnswerDetailDTO entity2dto(AnswerDetail answerDetail);
    AnswerDetail dto2entity(AnswerDetailDTO answerDetailDTO);

    List<AnswerDetailDTO> entity2dtoBatch(List<AnswerDetail> entities);
    List<AnswerDetail> dto2entityBatch(List<AnswerDetailDTO> dtos);
}
