package com.yzedulife.service.convert;

import com.yzedulife.service.dto.AnswerSheetDTO;
import com.yzedulife.service.entity.AnswerSheet;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 答卷转换器
 */
@Mapper
public interface AnswerSheetConvert {
    AnswerSheetConvert INSTANCE = Mappers.getMapper(AnswerSheetConvert.class);

    AnswerSheetDTO entity2dto(AnswerSheet answerSheet);
    AnswerSheet dto2entity(AnswerSheetDTO answerSheetDTO);

    List<AnswerSheetDTO> entity2dtoBatch(List<AnswerSheet> entities);
    List<AnswerSheet> dto2entityBatch(List<AnswerSheetDTO> dtos);
}
