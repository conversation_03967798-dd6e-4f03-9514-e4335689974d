package com.yzedulife.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.AnswerDetailDTO;
import com.yzedulife.service.entity.AnswerDetail;
import com.yzedulife.service.entity.Question;
import com.yzedulife.service.entity.QuestionOption;
import com.yzedulife.service.mapper.AnswerDetailMapper;
import com.yzedulife.service.mapper.QuestionMapper;
import com.yzedulife.service.mapper.QuestionOptionMapper;
import com.yzedulife.service.service.AnswerDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 答案详情服务实现类
 */
@Service
@Transactional
public class AnswerDetailServiceImpl implements AnswerDetailService {

    @Autowired
    private AnswerDetailMapper answerDetailMapper;
    
    @Autowired
    private QuestionMapper questionMapper;
    
    @Autowired
    private QuestionOptionMapper questionOptionMapper;

    @Override
    public AnswerDetailDTO getById(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("答案详情ID不能为空");
        }
        AnswerDetail answerDetail = answerDetailMapper.selectById(id);
        if (answerDetail == null) {
            throw new BusinessException("答案详情不存在");
        }
        return answerDetail.toDTO();
    }

    @Override
    public AnswerDetailDTO create(AnswerDetailDTO answerDetailDTO) throws BusinessException {
        if (answerDetailDTO == null) {
            throw new BusinessException("答案详情信息不能为空");
        }
        if (answerDetailDTO.getAnswerSheetId() == null) {
            throw new BusinessException("答卷ID不能为空");
        }
        if (answerDetailDTO.getQuestionId() == null) {
            throw new BusinessException("题目ID不能为空");
        }
        if (answerDetailDTO.getChosenOptionCode() == null || answerDetailDTO.getChosenOptionCode().trim().isEmpty()) {
            throw new BusinessException("所选选项代号不能为空");
        }
        
        // 检查同一答卷下是否已经回答了该题目
        AnswerDetail existingAnswer = answerDetailMapper.selectOne(new LambdaQueryWrapper<AnswerDetail>()
                .eq(AnswerDetail::getAnswerSheetId, answerDetailDTO.getAnswerSheetId())
                .eq(AnswerDetail::getQuestionId, answerDetailDTO.getQuestionId()));
        if (existingAnswer != null) {
            throw new BusinessException("该题目已经回答过了");
        }
        
        AnswerDetail answerDetail = answerDetailDTO.toEntity();
        answerDetail.setId(null); // 确保是新增
        
        // 自动判断答案正确性
        if (answerDetail.getIsCorrect() == null) {
            Question question = questionMapper.selectById(answerDetail.getQuestionId());
            if (question != null && question.getCorrectAnswerCode() != null) {
                // 直接通过选项代号比较
                answerDetail.setIsCorrect(question.getCorrectAnswerCode().equals(answerDetail.getChosenOptionCode()));
            }
        }
        
        int result = answerDetailMapper.insert(answerDetail);
        if (result <= 0) {
            throw new BusinessException("创建答案详情失败");
        }
        return answerDetail.toDTO();
    }

    @Override
    public AnswerDetailDTO update(AnswerDetailDTO answerDetailDTO) throws BusinessException {
        if (answerDetailDTO == null || answerDetailDTO.getId() == null) {
            throw new BusinessException("答案详情ID不能为空");
        }
        
        // 检查答案详情是否存在
        AnswerDetail existingAnswerDetail = answerDetailMapper.selectById(answerDetailDTO.getId());
        if (existingAnswerDetail == null) {
            throw new BusinessException("答案详情不存在");
        }
        
        AnswerDetail answerDetail = answerDetailDTO.toEntity();
        int result = answerDetailMapper.updateById(answerDetail);
        if (result <= 0) {
            throw new BusinessException("更新答案详情失败");
        }
        return answerDetailMapper.selectById(answerDetail.getId()).toDTO();
    }

    @Override
    public Boolean deleteById(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("答案详情ID不能为空");
        }
        
        AnswerDetail answerDetail = answerDetailMapper.selectById(id);
        if (answerDetail == null) {
            throw new BusinessException("答案详情不存在");
        }
        
        int result = answerDetailMapper.deleteById(id);
        return result > 0;
    }

    @Override
    public List<AnswerDetailDTO> getByAnswerSheetId(Long answerSheetId) throws BusinessException {
        if (answerSheetId == null) {
            throw new BusinessException("答卷ID不能为空");
        }
        List<AnswerDetail> answerDetails = answerDetailMapper.selectList(new LambdaQueryWrapper<AnswerDetail>()
                .eq(AnswerDetail::getAnswerSheetId, answerSheetId)
                .orderByAsc(AnswerDetail::getQuestionId));
        return answerDetails.stream().map(AnswerDetail::toDTO).collect(Collectors.toList());
    }

    @Override
    public List<AnswerDetailDTO> getByQuestionId(Long questionId) throws BusinessException {
        if (questionId == null) {
            throw new BusinessException("题目ID不能为空");
        }
        List<AnswerDetail> answerDetails = answerDetailMapper.selectList(new LambdaQueryWrapper<AnswerDetail>()
                .eq(AnswerDetail::getQuestionId, questionId)
                .orderByDesc(AnswerDetail::getId));
        return answerDetails.stream().map(AnswerDetail::toDTO).collect(Collectors.toList());
    }

    @Override
    public AnswerDetailDTO getByAnswerSheetIdAndQuestionId(Long answerSheetId, Long questionId) throws BusinessException {
        if (answerSheetId == null) {
            throw new BusinessException("答卷ID不能为空");
        }
        if (questionId == null) {
            throw new BusinessException("题目ID不能为空");
        }
        
        AnswerDetail answerDetail = answerDetailMapper.selectOne(new LambdaQueryWrapper<AnswerDetail>()
                .eq(AnswerDetail::getAnswerSheetId, answerSheetId)
                .eq(AnswerDetail::getQuestionId, questionId));
        
        if (answerDetail == null) {
            throw new BusinessException("答案详情不存在");
        }
        
        return answerDetail.toDTO();
    }

    @Override
    public Boolean batchCreate(List<AnswerDetailDTO> answerDetailDTOs) throws BusinessException {
        if (answerDetailDTOs == null || answerDetailDTOs.isEmpty()) {
            throw new BusinessException("答案详情列表不能为空");
        }
        
        for (AnswerDetailDTO dto : answerDetailDTOs) {
            create(dto);
        }
        
        return true;
    }

    @Override
    public Boolean batchDelete(List<Long> answerDetailIds) throws BusinessException {
        if (answerDetailIds == null || answerDetailIds.isEmpty()) {
            throw new BusinessException("答案详情ID列表不能为空");
        }
        
        for (Long answerDetailId : answerDetailIds) {
            deleteById(answerDetailId);
        }
        
        return true;
    }

    @Override
    public Boolean deleteByAnswerSheetId(Long answerSheetId) throws BusinessException {
        if (answerSheetId == null) {
            throw new BusinessException("答卷ID不能为空");
        }
        
        int result = answerDetailMapper.delete(new LambdaQueryWrapper<AnswerDetail>()
                .eq(AnswerDetail::getAnswerSheetId, answerSheetId));
        
        return result >= 0; // 即使没有答案详情也返回true
    }

    @Override
    public Integer calculateScore(Long answerSheetId) throws BusinessException {
        if (answerSheetId == null) {
            throw new BusinessException("答卷ID不能为空");
        }
        
        List<AnswerDetail> answerDetails = answerDetailMapper.selectList(new LambdaQueryWrapper<AnswerDetail>()
                .eq(AnswerDetail::getAnswerSheetId, answerSheetId));
        
        if (answerDetails.isEmpty()) {
            return 0;
        }
        
        int correctCount = 0;
        int totalCount = answerDetails.size();
        
        for (AnswerDetail detail : answerDetails) {
            if (Boolean.TRUE.equals(detail.getIsCorrect())) {
                correctCount++;
            }
        }
        
        // 计算得分（百分制）
        return Math.round((float) correctCount / totalCount * 100);
    }

    @Override
    public Boolean autoJudgeCorrectness(Long answerDetailId) throws BusinessException {
        if (answerDetailId == null) {
            throw new BusinessException("答案详情ID不能为空");
        }
        
        AnswerDetail answerDetail = answerDetailMapper.selectById(answerDetailId);
        if (answerDetail == null) {
            throw new BusinessException("答案详情不存在");
        }
        
        Question question = questionMapper.selectById(answerDetail.getQuestionId());
        if (question == null || question.getCorrectAnswerCode() == null) {
            return false; // 没有设置正确答案，无法判断
        }
        
        // 直接通过选项代号比较
        if (question.getCorrectAnswerCode() != null) {
            boolean isCorrect = question.getCorrectAnswerCode().equals(answerDetail.getChosenOptionCode());
            answerDetail.setIsCorrect(isCorrect);
        } else {
            return false; // 没有设置正确答案，无法判断
        }
        
        int result = answerDetailMapper.updateById(answerDetail);
        return result > 0;
    }

    @Override
    public Boolean batchAutoJudgeCorrectness(Long answerSheetId) throws BusinessException {
        if (answerSheetId == null) {
            throw new BusinessException("答卷ID不能为空");
        }
        
        List<AnswerDetail> answerDetails = answerDetailMapper.selectList(new LambdaQueryWrapper<AnswerDetail>()
                .eq(AnswerDetail::getAnswerSheetId, answerSheetId));
        
        for (AnswerDetail answerDetail : answerDetails) {
            autoJudgeCorrectness(answerDetail.getId());
        }
        
        return true;
    }

    @Override
    public Map<String, Integer> getAnswerStatistics(Long questionId) throws BusinessException {
        if (questionId == null) {
            throw new BusinessException("题目ID不能为空");
        }
        
        List<AnswerDetail> answerDetails = answerDetailMapper.selectList(new LambdaQueryWrapper<AnswerDetail>()
                .eq(AnswerDetail::getQuestionId, questionId));
        
        Map<String, Integer> statistics = new HashMap<>();
        
        // 获取该题目的所有选项
        List<QuestionOption> options = questionOptionMapper.selectList(new LambdaQueryWrapper<QuestionOption>()
                .eq(QuestionOption::getQuestionId, questionId));
        
        // 初始化统计数据
        for (QuestionOption option : options) {
            statistics.put(option.getOptionCode(), 0);
        }
        
        // 统计每个选项的选择次数
        for (AnswerDetail detail : answerDetails) {
            String chosenOptionCode = detail.getChosenOptionCode();
            if (chosenOptionCode != null) {
                statistics.put(chosenOptionCode,
                    statistics.getOrDefault(chosenOptionCode, 0) + 1);
            }
        }
        
        return statistics;
    }

    @Override
    public Map<String, Object> getQuestionnaireStatistics(Long questionnaireId) throws BusinessException {
        if (questionnaireId == null) {
            throw new BusinessException("问卷ID不能为空");
        }
        
        Map<String, Object> statistics = new HashMap<>();
        
        // 这里可以实现更复杂的问卷统计逻辑
        // 例如：总答题人数、平均分、各题目正确率等
        
        statistics.put("message", "问卷统计功能待实现");
        
        return statistics;
    }
}
