package com.yzedulife.service.convert;

import com.yzedulife.service.dto.StudentSchoolDTO;
import com.yzedulife.service.entity.StudentSchool;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 学生学校转换器
 */
@Mapper
public interface StudentSchoolConvert {
    StudentSchoolConvert INSTANCE = Mappers.getMapper(StudentSchoolConvert.class);

    StudentSchoolDTO entity2dto(StudentSchool studentSchool);
    StudentSchool dto2entity(StudentSchoolDTO studentSchoolDTO);

    List<StudentSchoolDTO> entity2dtoBatch(List<StudentSchool> entities);
    List<StudentSchool> dto2entityBatch(List<StudentSchoolDTO> dtos);
}
