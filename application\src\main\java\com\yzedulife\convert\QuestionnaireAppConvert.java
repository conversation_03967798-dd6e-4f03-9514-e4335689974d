package com.yzedulife.convert;

import com.yzedulife.response.QuestionnaireResponse;
import com.yzedulife.service.dto.QuestionnaireDTO;
import com.yzedulife.vo.QuestionnaireVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 问卷应用层转换器
 */
@Mapper
public interface QuestionnaireAppConvert {
    QuestionnaireAppConvert INSTANCE = Mappers.getMapper(QuestionnaireAppConvert.class);

    /**
     * VO转DTO
     */
    QuestionnaireDTO vo2dto(QuestionnaireVO vo);

    /**
     * DTO转Response
     */
    QuestionnaireResponse dto2response(QuestionnaireDTO dto);

    /**
     * DTO列表转Response列表
     */
    List<QuestionnaireResponse> dto2responseList(List<QuestionnaireDTO> dtoList);
}
