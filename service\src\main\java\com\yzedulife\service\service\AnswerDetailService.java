package com.yzedulife.service.service;

import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.AnswerDetailDTO;

import java.util.List;
import java.util.Map;

/**
 * 答案详情服务接口
 */
public interface AnswerDetailService {

    /**
     * 根据ID获取答案详情
     */
    AnswerDetailDTO getById(Long id) throws BusinessException;

    /**
     * 创建答案详情
     */
    AnswerDetailDTO create(AnswerDetailDTO answerDetailDTO) throws BusinessException;

    /**
     * 更新答案详情
     */
    AnswerDetailDTO update(AnswerDetailDTO answerDetailDTO) throws BusinessException;

    /**
     * 删除答案详情
     */
    Boolean deleteById(Long id) throws BusinessException;

    /**
     * 根据答卷ID获取所有答案详情
     */
    List<AnswerDetailDTO> getByAnswerSheetId(Long answerSheetId) throws BusinessException;

    /**
     * 根据题目ID获取所有答案详情
     */
    List<AnswerDetailDTO> getByQuestionId(Long questionId) throws BusinessException;

    /**
     * 根据答卷ID和题目ID获取答案详情
     */
    AnswerDetailDTO getByAnswerSheetIdAndQuestionId(Long answerSheetId, Long questionId) throws BusinessException;

    /**
     * 批量创建答案详情
     */
    Boolean batchCreate(List<AnswerDetailDTO> answerDetailDTOs) throws BusinessException;

    /**
     * 批量删除答案详情
     */
    Boolean batchDelete(List<Long> answerDetailIds) throws BusinessException;

    /**
     * 删除答卷的所有答案详情
     */
    Boolean deleteByAnswerSheetId(Long answerSheetId) throws BusinessException;

    /**
     * 计算答卷得分
     */
    Integer calculateScore(Long answerSheetId) throws BusinessException;

    /**
     * 自动判断答案正确性
     */
    Boolean autoJudgeCorrectness(Long answerDetailId) throws BusinessException;

    /**
     * 批量自动判断答案正确性
     */
    Boolean batchAutoJudgeCorrectness(Long answerSheetId) throws BusinessException;

    /**
     * 获取题目的答案统计
     */
    Map<String, Integer> getAnswerStatistics(Long questionId) throws BusinessException;

    /**
     * 获取问卷的答题统计
     */
    Map<String, Object> getQuestionnaireStatistics(Long questionnaireId) throws BusinessException;
}
