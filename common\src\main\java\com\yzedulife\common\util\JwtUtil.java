package com.yzedulife.common.util;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

import java.util.UUID;

public class JwtUtil {
    private static String secretKey = "j132o132k132e132r";

    public static void setSecretKey(String key) {
        secretKey = key;
    }

    public static String createToken(String type, String id) {
        JwtBuilder jwtBuilder = Jwts.builder();
        String token = jwtBuilder
                .setHeaderParam("typ", "JWT")
                .setHeaderParam("alg", "HS256")
                .claim("type", type)
                .claim("id", id)
                .setSubject("token")
                .setId(UUID.randomUUID().toString())
                .signWith(SignatureAlgorithm.HS256, secretKey)
                .compact();
        return token;
    }

    public static Boolean checkToken(String token) {
        if (StringUtil.isBlank(token)) return false;
        try {
            Jwts.parser().setSigningKey(secretKey).parseClaimsJws(token);
        }
        catch (Exception e) {
            return false;
        }
        return true;
    }

    public static String getType(String token) {
        Claims body = Jwts.parser().setSigningKey(secretKey).parseClaimsJws(token).getBody();
        return body.get("type").toString();
    }
    public static String getId(String token) {
        Claims body = Jwts.parser().setSigningKey(secretKey).parseClaimsJws(token).getBody();
        return body.get("id").toString();
    }

}
