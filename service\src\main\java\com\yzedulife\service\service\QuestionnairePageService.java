package com.yzedulife.service.service;

import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.QuestionnairePageDTO;

import java.util.List;

/**
 * 问卷页面服务接口
 */
public interface QuestionnairePageService {

    /**
     * 根据ID获取问卷页面
     */
    QuestionnairePageDTO getById(Long id) throws BusinessException;

    /**
     * 创建问卷页面
     */
    QuestionnairePageDTO create(QuestionnairePageDTO questionnairePageDTO) throws BusinessException;

    /**
     * 更新问卷页面
     */
    QuestionnairePageDTO update(QuestionnairePageDTO questionnairePageDTO) throws BusinessException;

    /**
     * 删除问卷页面
     */
    Boolean deleteById(Long id) throws BusinessException;

    /**
     * 根据问卷ID获取所有页面
     */
    List<QuestionnairePageDTO> getByQuestionnaireId(Long questionnaireId) throws BusinessException;

    /**
     * 根据问卷ID和页码获取页面
     */
    QuestionnairePageDTO getByQuestionnaireIdAndPageNumber(Long questionnaireId, Integer pageNumber) throws BusinessException;

    /**
     * 获取问卷的页面总数
     */
    Integer getPageCountByQuestionnaireId(Long questionnaireId) throws BusinessException;

    /**
     * 调整页面顺序
     */
    Boolean adjustPageOrder(Long questionnaireId, List<Long> pageIds) throws BusinessException;
}
