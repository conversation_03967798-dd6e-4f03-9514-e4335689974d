package com.yzedulife.service.convert;

import com.yzedulife.service.dto.AdminDTO;
import com.yzedulife.service.entity.Admin;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 管理员转换器
 */
@Mapper
public interface AdminConvert {
    AdminConvert INSTANCE = Mappers.getMapper(AdminConvert.class);

    AdminDTO entity2dto(Admin admin);
    Admin dto2entity(AdminDTO adminDTO);

    List<AdminDTO> entity2dtoBatch(List<Admin> entities);
    List<Admin> dto2entityBatch(List<AdminDTO> dtos);
}
