package com.yzedulife.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 学生班级VO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "学生班级VO")
public class StudentClassVO {

    @Schema(description = "班级ID")
    private Long id;

    @Schema(description = "班级名称", required = true)
    @NotBlank(message = "班级名称不能为空")
    private String className;

    @Schema(description = "学校ID")
    private Long schoolId;
}
