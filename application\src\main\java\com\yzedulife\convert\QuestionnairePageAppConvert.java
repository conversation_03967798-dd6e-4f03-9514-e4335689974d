package com.yzedulife.convert;

import com.yzedulife.response.QuestionnairePageResponse;
import com.yzedulife.service.dto.QuestionnairePageDTO;
import com.yzedulife.vo.QuestionnairePageVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 问卷页应用层转换器
 */
@Mapper
public interface QuestionnairePageAppConvert {
    QuestionnairePageAppConvert INSTANCE = Mappers.getMapper(QuestionnairePageAppConvert.class);

    /**
     * VO转DTO
     */
    QuestionnairePageDTO vo2dto(QuestionnairePageVO vo);

    /**
     * DTO转Response
     */
    QuestionnairePageResponse dto2response(QuestionnairePageDTO dto);
}
