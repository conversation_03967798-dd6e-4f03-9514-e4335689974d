package com.yzedulife.util;

import com.yzedulife.common.util.JwtUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("JWT工具类测试")
class JwtUtilTest {

    @BeforeEach
    void setUp() {
        // 设置测试用的密钥
        JwtUtil.setSecretKey("test-secret-key-for-junit-testing");
    }

    @Test
    @DisplayName("创建Token - 管理员")
    void createToken_Admin() {
        // When
        String token = JwtUtil.createToken("admin", "1");

        // Then
        assertNotNull(token);
        assertFalse(token.isEmpty());
        assertTrue(token.contains("."));
    }

    @Test
    @DisplayName("创建Token - 学生")
    void createToken_Student() {
        // When
        String token = JwtUtil.createToken("student", "1");

        // Then
        assertNotNull(token);
        assertFalse(token.isEmpty());
        assertTrue(token.contains("."));
    }

    @Test
    @DisplayName("创建Token - 社会人士")
    void createToken_Other() {
        // When
        String token = JwtUtil.createToken("other", "1");

        // Then
        assertNotNull(token);
        assertFalse(token.isEmpty());
        assertTrue(token.contains("."));
    }

    @Test
    @DisplayName("验证Token - 有效Token")
    void checkToken_ValidToken() {
        // Given
        String token = JwtUtil.createToken("admin", "1");

        // When
        Boolean result = JwtUtil.checkToken(token);

        // Then
        assertTrue(result);
    }

    @Test
    @DisplayName("验证Token - 无效Token")
    void checkToken_InvalidToken() {
        // Given
        String invalidToken = "invalid.token.here";

        // When
        Boolean result = JwtUtil.checkToken(invalidToken);

        // Then
        assertFalse(result);
    }

    @Test
    @DisplayName("验证Token - 空Token")
    void checkToken_EmptyToken() {
        // When
        Boolean result = JwtUtil.checkToken("");

        // Then
        assertFalse(result);
    }

    @Test
    @DisplayName("验证Token - null Token")
    void checkToken_NullToken() {
        // When
        Boolean result = JwtUtil.checkToken(null);

        // Then
        assertFalse(result);
    }

    @Test
    @DisplayName("获取用户类型 - 管理员")
    void getType_Admin() {
        // Given
        String token = JwtUtil.createToken("admin", "1");

        // When
        String type = JwtUtil.getType(token);

        // Then
        assertEquals("admin", type);
    }

    @Test
    @DisplayName("获取用户类型 - 学生")
    void getType_Student() {
        // Given
        String token = JwtUtil.createToken("student", "2");

        // When
        String type = JwtUtil.getType(token);

        // Then
        assertEquals("student", type);
    }

    @Test
    @DisplayName("获取用户类型 - 社会人士")
    void getType_Other() {
        // Given
        String token = JwtUtil.createToken("other", "3");

        // When
        String type = JwtUtil.getType(token);

        // Then
        assertEquals("other", type);
    }

    @Test
    @DisplayName("获取用户ID - 管理员")
    void getId_Admin() {
        // Given
        String token = JwtUtil.createToken("admin", "123");

        // When
        String id = JwtUtil.getId(token);

        // Then
        assertEquals("123", id);
    }

    @Test
    @DisplayName("获取用户ID - 学生")
    void getId_Student() {
        // Given
        String token = JwtUtil.createToken("student", "456");

        // When
        String id = JwtUtil.getId(token);

        // Then
        assertEquals("456", id);
    }

    @Test
    @DisplayName("获取用户ID - 社会人士")
    void getId_Other() {
        // Given
        String token = JwtUtil.createToken("other", "789");

        // When
        String id = JwtUtil.getId(token);

        // Then
        assertEquals("789", id);
    }

    @Test
    @DisplayName("Token完整性测试 - 创建后验证")
    void tokenIntegrity_CreateAndVerify() {
        // Given
        String userType = "admin";
        String userId = "100";

        // When
        String token = JwtUtil.createToken(userType, userId);
        Boolean isValid = JwtUtil.checkToken(token);
        String extractedType = JwtUtil.getType(token);
        String extractedId = JwtUtil.getId(token);

        // Then
        assertTrue(isValid);
        assertEquals(userType, extractedType);
        assertEquals(userId, extractedId);
    }

    @Test
    @DisplayName("不同密钥验证Token - 应该失败")
    void checkToken_DifferentSecretKey() {
        // Given
        String token = JwtUtil.createToken("admin", "1");
        
        // 更改密钥
        JwtUtil.setSecretKey("different-secret-key");

        // When
        Boolean result = JwtUtil.checkToken(token);

        // Then
        assertFalse(result);
    }

    @Test
    @DisplayName("Token格式验证 - 包含三个部分")
    void tokenFormat_ThreeParts() {
        // Given
        String token = JwtUtil.createToken("admin", "1");

        // When
        String[] parts = token.split("\\.");

        // Then
        assertEquals(3, parts.length);
        assertTrue(parts[0].length() > 0); // Header
        assertTrue(parts[1].length() > 0); // Payload
        assertTrue(parts[2].length() > 0); // Signature
    }

    @Test
    @DisplayName("多次创建Token - 应该不同")
    void createToken_MultipleTimes_ShouldBeDifferent() {
        // When
        String token1 = JwtUtil.createToken("admin", "1");
        String token2 = JwtUtil.createToken("admin", "1");

        // Then
        assertNotEquals(token1, token2); // 因为包含随机的jti
    }


}
