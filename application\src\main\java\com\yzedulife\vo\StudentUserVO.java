package com.yzedulife.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 学生用户VO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "学生用户VO")
public class StudentUserVO {

    @Schema(description = "学生ID")
    private Long id;

    @Schema(description = "姓名", required = true)
    @NotBlank(message = "姓名不能为空")
    private String name;

    @Schema(description = "学号", required = true)
    @NotBlank(message = "学号不能为空")
    private String studentNumber;

    @Schema(description = "班级ID", required = true)
    @NotNull(message = "班级ID不能为空")
    private Long classId;
}
