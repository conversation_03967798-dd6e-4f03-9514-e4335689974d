package com.yzedulife.controller;

import com.yzedulife.vo.AnswerSubmitVO;
import com.yzedulife.convert.AnswerDetailAppConvert;
import com.yzedulife.service.dto.AnswerDetailDTO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 答案提交功能测试
 */
@SpringBootTest
public class AnswerSubmitTest {

    @Test
    public void testAnswerSubmitVOConversion() {
        // 创建 AnswerSubmitVO
        AnswerSubmitVO submitVO = new AnswerSubmitVO();
        submitVO.setQuestionId(1L);
        submitVO.setChosenOptionCode("A");

        // 转换为 AnswerDetailDTO
        AnswerDetailDTO detailDTO = AnswerDetailAppConvert.INSTANCE.submitVo2dto(submitVO);

        // 验证转换结果
        assertNotNull(detailDTO);
        assertEquals(1L, detailDTO.getQuestionId());
        assertEquals("A", detailDTO.getChosenOptionCode());
        assertNull(detailDTO.getAnswerSheetId()); // 应该为空，等待后续设置
    }

    @Test
    public void testAnswerSubmitVOValidation() {
        // 测试 AnswerSubmitVO 的字段
        AnswerSubmitVO submitVO = new AnswerSubmitVO();
        
        // 验证初始状态
        assertNull(submitVO.getQuestionId());
        assertNull(submitVO.getChosenOptionCode());
        
        // 设置值
        submitVO.setQuestionId(2L);
        submitVO.setChosenOptionCode("B");
        
        // 验证设置后的值
        assertEquals(2L, submitVO.getQuestionId());
        assertEquals("B", submitVO.getChosenOptionCode());
    }
}
