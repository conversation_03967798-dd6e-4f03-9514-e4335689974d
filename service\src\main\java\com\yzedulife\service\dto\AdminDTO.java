package com.yzedulife.service.dto;

import com.yzedulife.service.convert.AdminConvert;
import com.yzedulife.service.entity.Admin;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 管理员DTO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class AdminDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 管理员ID
     */
    private Long id;

    /**
     * 用户名 (唯一)
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 角色
     */
    private String role;

    public Admin toEntity() {
        return AdminConvert.INSTANCE.dto2entity(this);
    }
}
