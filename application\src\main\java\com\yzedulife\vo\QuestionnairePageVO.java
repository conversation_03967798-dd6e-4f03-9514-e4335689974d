package com.yzedulife.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 问卷页面请求VO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "问卷页面请求VO")
public class QuestionnairePageVO {
    
    @Schema(description = "页面ID（更新时需要）")
    private Long id;
    
    @Schema(description = "问卷ID", required = true)
    @NotNull(message = "问卷ID不能为空")
    private Long questionnaireId;
    
    @Schema(description = "页码", required = true)
    @NotNull(message = "页码不能为空")
    private Integer pageNumber;
}
