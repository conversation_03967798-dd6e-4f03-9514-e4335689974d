package com.yzedulife.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 问卷基本信息响应
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "问卷基本信息响应")
public class QuestionnaireResponse {
    
    @Schema(description = "问卷ID")
    private Long id;
    
    @Schema(description = "问卷标题")
    private String title;

    @Schema(description = "描述（图片路径）")
    private String description;

    @Schema(description = "目标受众", example = "STUDENT 或 SOCIAL")
    private String targetAudience;
    
    @Schema(description = "问卷状态", example = "0-草稿, 1-发布, 2-结束")
    private Integer status;
    
    @Schema(description = "创建者ID")
    private Long creatorId;

    @Schema(description = "问卷页面列表")
    private List<QuestionnairePageResponse> pages;
}
