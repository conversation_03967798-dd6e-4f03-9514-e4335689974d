package com.yzedulife.service.service;

import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.StudentSchoolDTO;

import java.util.List;

/**
 * 学生学校服务接口
 */
public interface StudentSchoolService {

    /**
     * 根据ID获取学校
     */
    StudentSchoolDTO getById(Long id) throws BusinessException;

    /**
     * 根据学校名称获取学校
     */
    StudentSchoolDTO getBySchoolName(String schoolName) throws BusinessException;

    /**
     * 创建学校
     */
    StudentSchoolDTO create(StudentSchoolDTO studentSchoolDTO) throws BusinessException;

    /**
     * 更新学校
     */
    StudentSchoolDTO update(StudentSchoolDTO studentSchoolDTO) throws BusinessException;

    /**
     * 删除学校
     */
    Boolean deleteById(Long id) throws BusinessException;

    /**
     * 获取所有学校
     */
    List<StudentSchoolDTO> getAll() throws BusinessException;

    /**
     * 检查学校是否存在
     */
    Boolean isExist(Long id) throws BusinessException;

    /**
     * 检查学校名称是否存在
     */
    Boolean isSchoolNameExist(String schoolName) throws BusinessException;
}
