package com.yzedulife.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 答卷请求VO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "答卷请求VO")
public class AnswerSheetVO {
    
    @Schema(description = "答卷ID（更新时需要）")
    private Long id;
    
    @Schema(description = "问卷ID", required = true)
    @NotNull(message = "问卷ID不能为空")
    private Long questionnaireId;
}
