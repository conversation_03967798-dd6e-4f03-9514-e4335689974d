package com.yzedulife.service.service;

import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.QuestionnaireDTO;

import java.util.List;

/**
 * 问卷服务接口
 */
public interface QuestionnaireService {

    /**
     * 根据ID获取问卷
     */
    QuestionnaireDTO getById(Long id) throws BusinessException;

    /**
     * 创建问卷
     */
    QuestionnaireDTO create(QuestionnaireDTO questionnaireDTO) throws BusinessException;

    /**
     * 更新问卷
     */
    QuestionnaireDTO update(QuestionnaireDTO questionnaireDTO) throws BusinessException;

    /**
     * 删除问卷
     */
    Boolean deleteById(Long id) throws BusinessException;

    /**
     * 获取所有问卷
     */
    List<QuestionnaireDTO> getAll() throws BusinessException;

    /**
     * 根据创建者ID获取问卷列表
     */
    List<QuestionnaireDTO> getByCreatorId(Long creatorId) throws BusinessException;

    /**
     * 发布问卷
     */
    Boolean publish(Long id) throws BusinessException;

    /**
     * 关闭问卷
     */
    Boolean close(Long id) throws BusinessException;

    /**
     * 复制问卷
     */
    QuestionnaireDTO copy(Long id, Long newCreatorId) throws BusinessException;
}
