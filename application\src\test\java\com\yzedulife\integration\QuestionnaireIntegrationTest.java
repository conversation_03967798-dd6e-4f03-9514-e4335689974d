package com.yzedulife.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yzedulife.common.util.JwtUtil;
import com.yzedulife.util.TestDataFactory;
import com.yzedulife.vo.AnswerDetailVO;
import com.yzedulife.vo.QuestionnaireVO;
import com.yzedulife.vo.QuestionVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("问卷系统集成测试")
class QuestionnaireIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    private String adminToken;
    private String studentToken;
    private String otherToken;

    @BeforeEach
    void setUp() {
        // 设置JWT密钥
        JwtUtil.setSecretKey("test-secret-key-for-junit-testing");
        
        // 创建测试用的Token
        adminToken = "Bearer " + JwtUtil.createToken("admin", "1");
        studentToken = "Bearer " + JwtUtil.createToken("student", "1");
        otherToken = "Bearer " + JwtUtil.createToken("other", "1");
    }

    @Test
    @DisplayName("完整问卷流程测试 - 管理员登录、创建问卷、学生答题")
    void completeQuestionnaireFlow() throws Exception {
        // 1. 管理员登录
        mockMvc.perform(post("/auth/loginAdmin")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                        .param("username", "testadmin")
                        .param("password", "testpassword"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.token").exists());

        // 2. 验证管理员身份
        mockMvc.perform(get("/verifyAdmin")
                        .header("Authorization", adminToken))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.msg").value("管理员身份验证成功"));

        // 3. 创建问卷
        QuestionnaireVO questionnaireVO = TestDataFactory.createQuestionnaireVO();
        questionnaireVO.setTitle("集成测试问卷");
        
        mockMvc.perform(post("/questionnaire/create")
                        .header("Authorization", adminToken)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(questionnaireVO)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.title").value("集成测试问卷"));

        // 4. 查看问卷列表
        mockMvc.perform(get("/questionnaire/list")
                        .header("Authorization", adminToken))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());

        // 5. 学生登录
        mockMvc.perform(post("/auth/loginStudent")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                        .param("name", "张三")
                        .param("studentNumber", "2023001"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.token").exists());

        // 6. 学生查看问卷详情
        mockMvc.perform(get("/questionnaire/detail")
                        .header("Authorization", studentToken)
                        .param("id", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value(1));

        // 7. 学生提交答案
        List<AnswerSubmitVO> answerDetails = TestDataFactory.createAnswerSubmitVOList();
        
        mockMvc.perform(post("/answer/submit")
                        .header("Authorization", studentToken)
                        .param("questionnaireId", "1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(answerDetails)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.msg").value("答案提交成功"));

        // 8. 管理员查看答案
        mockMvc.perform(get("/answer/list")
                        .header("Authorization", adminToken)
                        .param("questionnaireId", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());
    }

    @Test
    @DisplayName("社会人士完整流程测试 - 发送验证码、登录、答题")
    void otherUserCompleteFlow() throws Exception {
        // 1. 发送验证码
        mockMvc.perform(post("/auth/sendCode")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                        .param("phone", "13800138000"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.msg").value("验证码发送成功"));

        // 2. 社会人士登录（这里需要模拟验证码验证）
        // 注意：在实际测试中，验证码验证可能需要特殊处理
        
        // 3. 查看问卷详情
        mockMvc.perform(get("/questionnaire/detail")
                        .header("Authorization", otherToken)
                        .param("id", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // 4. 提交答案
        List<AnswerSubmitVO> answerDetails = TestDataFactory.createAnswerSubmitVOList();
        
        mockMvc.perform(post("/answer/submit")
                        .header("Authorization", otherToken)
                        .param("questionnaireId", "1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(answerDetails)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @DisplayName("权限控制测试 - 验证不同用户的权限边界")
    void permissionControlTest() throws Exception {
        // 1. 学生尝试创建问卷（应该失败）
        QuestionnaireVO questionnaireVO = TestDataFactory.createQuestionnaireVO();
        
        mockMvc.perform(post("/questionnaire/create")
                        .header("Authorization", studentToken)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(questionnaireVO)))
                .andDo(print())
                .andExpect(status().isForbidden());

        // 2. 社会人士尝试查看答案列表（应该失败）
        mockMvc.perform(get("/answer/list")
                        .header("Authorization", otherToken))
                .andDo(print())
                .andExpect(status().isForbidden());

        // 3. 管理员尝试提交答案（应该失败）
        List<AnswerSubmitVO> answerDetails = TestDataFactory.createAnswerSubmitVOList();
        
        mockMvc.perform(post("/answer/submit")
                        .header("Authorization", adminToken)
                        .param("questionnaireId", "1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(answerDetails)))
                .andDo(print())
                .andExpect(status().isForbidden());

        // 4. 无Token访问需要权限的接口（应该失败）
        mockMvc.perform(get("/verifyAdmin"))
                .andDo(print())
                .andExpect(status().isForbidden());
    }

    @Test
    @DisplayName("题目管理流程测试 - 创建、修改、删除题目")
    void questionManagementFlow() throws Exception {
        // 1. 创建题目
        QuestionVO questionVO = TestDataFactory.createQuestionVO();
        questionVO.setContent("集成测试题目");
        
        mockMvc.perform(post("/question/create")
                        .header("Authorization", adminToken)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(questionVO)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.content").value("集成测试题目"));

        // 2. 修改题目
        questionVO.setId(1L);
        questionVO.setContent("修改后的题目");
        
        mockMvc.perform(post("/question/update")
                        .header("Authorization", adminToken)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(questionVO)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // 3. 删除题目
        mockMvc.perform(post("/question/delete")
                        .header("Authorization", adminToken)
                        .param("id", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.msg").value("删除成功"));
    }

    @Test
    @DisplayName("错误处理测试 - 验证各种异常情况的处理")
    void errorHandlingTest() throws Exception {
        // 1. 访问不存在的问卷
        mockMvc.perform(get("/questionnaire/detail")
                        .header("Authorization", studentToken)
                        .param("id", "999"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false));

        // 2. 提交空的答案列表
        mockMvc.perform(post("/answer/submit")
                        .header("Authorization", studentToken)
                        .param("questionnaireId", "1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("[]"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false));

        // 3. 删除不存在的问卷
        mockMvc.perform(post("/questionnaire/delete")
                        .header("Authorization", adminToken)
                        .param("id", "999"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false));

        // 4. 使用无效的Token
        mockMvc.perform(get("/verifyAdmin")
                        .header("Authorization", "Bearer invalid-token"))
                .andDo(print())
                .andExpect(status().isForbidden());
    }
}
