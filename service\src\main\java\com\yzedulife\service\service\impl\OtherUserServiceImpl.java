package com.yzedulife.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.OtherUserDTO;
import com.yzedulife.service.entity.OtherUser;
import com.yzedulife.service.mapper.OtherUserMapper;
import com.yzedulife.service.service.OtherUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 社会人士用户服务实现类
 */
@Service
@Transactional
public class OtherUserServiceImpl implements OtherUserService {

    @Autowired
    private OtherUserMapper otherUserMapper;

    @Override
    public OtherUserDTO getById(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("社会人士用户ID不能为空");
        }
        OtherUser otherUser = otherUserMapper.selectById(id);
        if (otherUser == null) {
            throw new BusinessException("社会人士用户不存在");
        }
        return otherUser.toDTO();
    }

    @Override
    public OtherUserDTO getByPhone(String phone) throws BusinessException {
        if (!StringUtils.hasText(phone)) {
            throw new BusinessException("手机号不能为空");
        }
        OtherUser otherUser = otherUserMapper.selectOne(new LambdaQueryWrapper<OtherUser>()
                .eq(OtherUser::getPhone, phone));
        if (otherUser == null) {
            throw new BusinessException("社会人士用户不存在");
        }
        return otherUser.toDTO();
    }

    @Override
    public OtherUserDTO create(OtherUserDTO otherUserDTO) throws BusinessException {
        if (otherUserDTO == null) {
            throw new BusinessException("社会人士用户信息不能为空");
        }
        if (!StringUtils.hasText(otherUserDTO.getPhone())) {
            throw new BusinessException("手机号不能为空");
        }
        
        // 检查手机号是否已存在
        if (isPhoneExist(otherUserDTO.getPhone())) {
            throw new BusinessException("手机号已存在");
        }
        
        OtherUser otherUser = otherUserDTO.toEntity();
        otherUser.setId(null); // 确保是新增
        int result = otherUserMapper.insert(otherUser);
        if (result <= 0) {
            throw new BusinessException("创建社会人士用户失败");
        }
        return otherUser.toDTO();
    }

    @Override
    public OtherUserDTO update(OtherUserDTO otherUserDTO) throws BusinessException {
        if (otherUserDTO == null || otherUserDTO.getId() == null) {
            throw new BusinessException("社会人士用户ID不能为空");
        }
        
        // 检查用户是否存在
        OtherUser existingUser = otherUserMapper.selectById(otherUserDTO.getId());
        if (existingUser == null) {
            throw new BusinessException("社会人士用户不存在");
        }
        
        // 如果修改了手机号，检查新手机号是否已存在
        if (StringUtils.hasText(otherUserDTO.getPhone()) && 
            !otherUserDTO.getPhone().equals(existingUser.getPhone())) {
            if (isPhoneExist(otherUserDTO.getPhone())) {
                throw new BusinessException("手机号已存在");
            }
        }
        
        OtherUser otherUser = otherUserDTO.toEntity();
        int result = otherUserMapper.updateById(otherUser);
        if (result <= 0) {
            throw new BusinessException("更新社会人士用户失败");
        }
        return otherUserMapper.selectById(otherUser.getId()).toDTO();
    }

    @Override
    public Boolean deleteById(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("社会人士用户ID不能为空");
        }
        
        OtherUser otherUser = otherUserMapper.selectById(id);
        if (otherUser == null) {
            throw new BusinessException("社会人士用户不存在");
        }
        
        int result = otherUserMapper.deleteById(id);
        return result > 0;
    }

    @Override
    public List<OtherUserDTO> getAll() throws BusinessException {
        List<OtherUser> otherUsers = otherUserMapper.selectList(new LambdaQueryWrapper<OtherUser>()
                .orderByDesc(OtherUser::getId));
        return otherUsers.stream().map(OtherUser::toDTO).collect(java.util.stream.Collectors.toList());
    }

    @Override
    public Boolean isExist(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("社会人士用户ID不能为空");
        }
        return otherUserMapper.exists(new LambdaQueryWrapper<OtherUser>()
                .eq(OtherUser::getId, id));
    }

    @Override
    public Boolean isPhoneExist(String phone) throws BusinessException {
        if (!StringUtils.hasText(phone)) {
            return false;
        }
        return otherUserMapper.exists(new LambdaQueryWrapper<OtherUser>()
                .eq(OtherUser::getPhone, phone));
    }
}
