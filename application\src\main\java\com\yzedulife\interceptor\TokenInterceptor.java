package com.yzedulife.interceptor;

import com.yzedulife.annotation.Token;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.common.domain.CommonErrorCode;
import com.yzedulife.common.util.JwtUtil;
import com.yzedulife.service.service.AdminService;
import com.yzedulife.service.service.OtherUserService;
import com.yzedulife.service.service.StudentUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

@Component
public class TokenInterceptor extends HandlerInterceptorAdapter {

    @Autowired
    private AdminService adminService;
    @Autowired
    private StudentUserService studentUserService;
    @Autowired
    private OtherUserService otherUserService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        Token annotation;
        if (handler instanceof HandlerMethod) {
            annotation = ((HandlerMethod) handler).getMethodAnnotation(Token.class);
        }
        else
            return true;

        if (annotation != null) { // 身份校验
            if (request.getHeaders("Authorization").hasMoreElements()) {
                String token = request.getHeader("Authorization").replace("Bearer ", "");
                String type = JwtUtil.getType(token);
                if (JwtUtil.checkToken(token)) { // 校验token
                    List<String> groups = Arrays.asList(annotation.value().split(" "));
                    if (groups.contains(type)) { // 校验用户类型
                        String id = JwtUtil.getId(token);
                        if (type.equals("admin")) {
                            if (adminService.isExist(Long.parseLong(id))) return true;
                        } else if (type.equals("student")) {
                            if (studentUserService.isExist(Long.parseLong(id))) return true;
                        } else if (type.equals("other")) {
                            if (otherUserService.isExist(Long.parseLong(id))) return true;
                        }
                    }
                }
            }
        }
        else // 无需校验
            return true;

        // 验证不通过，抛出异常，表示用户未登录
        throw new BusinessException(CommonErrorCode.E_NO_AUTHORITY);
    }

}
