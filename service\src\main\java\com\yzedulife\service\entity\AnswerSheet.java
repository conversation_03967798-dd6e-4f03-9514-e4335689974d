package com.yzedulife.service.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yzedulife.service.convert.AnswerSheetConvert;
import com.yzedulife.service.dto.AnswerSheetDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * 答卷实体类
 */
@Data
@TableName("answer_sheets")
public class AnswerSheet implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 答卷ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 所属问卷ID
     */
    @TableField("questionnaire_id")
    private Long questionnaireId;

    /**
     * 提交者类型
     */
    @TableField("submitter_type")
    private String submitterType;

    /**
     * 学生ID (nullable)
     */
    @TableField("student_user_id")
    private Long studentUserId;

    /**
     * 社会人士ID (nullable)
     */
    @TableField("other_user_id")
    private Long otherUserId;

    /**
     * 提交时间
     */
    @TableField("submit_time")
    private java.time.LocalDateTime submitTime;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private java.time.LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField("updated_time")
    private java.time.LocalDateTime updatedTime;

    public AnswerSheetDTO toDTO() {
        return AnswerSheetConvert.INSTANCE.entity2dto(this);
    }
}
