package com.yzedulife.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.QuestionnaireDTO;
import com.yzedulife.service.dto.QuestionnairePageDTO;
import com.yzedulife.service.dto.QuestionDTO;
import com.yzedulife.service.dto.QuestionOptionDTO;
import com.yzedulife.service.service.QuestionnaireService;
import com.yzedulife.service.service.QuestionnairePageService;
import com.yzedulife.service.service.QuestionService;
import com.yzedulife.service.service.QuestionOptionService;
import com.yzedulife.util.MockUtil;
import com.yzedulife.util.TestDataFactory;
import com.yzedulife.vo.QuestionnaireVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(QuestionnaireController.class)
@ActiveProfiles("test")
@DisplayName("问卷控制器测试")
class QuestionnaireControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private QuestionnaireService questionnaireService;

    @MockBean
    private QuestionnairePageService questionnairePageService;

    @MockBean
    private QuestionService questionService;

    @MockBean
    private QuestionOptionService questionOptionService;

    private QuestionnaireDTO testQuestionnaire;
    private QuestionnaireVO testQuestionnaireVO;
    private List<QuestionnaireDTO> testQuestionnaireList;

    @BeforeEach
    void setUp() {
        testQuestionnaire = TestDataFactory.createQuestionnaireDTO();
        testQuestionnaireVO = TestDataFactory.createQuestionnaireVO();
        
        testQuestionnaireList = new ArrayList<>();
        testQuestionnaireList.add(testQuestionnaire);
        
        QuestionnaireDTO questionnaire2 = TestDataFactory.createQuestionnaireDTO();
        questionnaire2.setId(2L);
        questionnaire2.setTitle("测试问卷2");
        testQuestionnaireList.add(questionnaire2);
    }

    @Test
    @DisplayName("创建问卷 - 成功")
    void create_Success() throws Exception {
        // Given
        when(questionnaireService.create(any(QuestionnaireDTO.class)))
                .thenReturn(testQuestionnaire);

        // When & Then
        mockMvc.perform(MockUtil.postWithAdminToken("/questionnaire/create")
                        .content(MockUtil.asJsonString(testQuestionnaireVO)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.title").value("测试问卷"));

        verify(questionnaireService).create(any(QuestionnaireDTO.class));
    }

    @Test
    @DisplayName("创建问卷 - 业务异常")
    void create_BusinessException() throws Exception {
        // Given
        when(questionnaireService.create(any(QuestionnaireDTO.class)))
                .thenThrow(new BusinessException("问卷标题不能为空"));

        // When & Then
        mockMvc.perform(MockUtil.postWithAdminToken("/questionnaire/create")
                        .content(MockUtil.asJsonString(testQuestionnaireVO)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.msg").value("问卷标题不能为空"));

        verify(questionnaireService).create(any(QuestionnaireDTO.class));
    }

    @Test
    @DisplayName("创建问卷 - 无权限")
    void create_NoPermission() throws Exception {
        // When & Then
        mockMvc.perform(MockUtil.postWithStudentToken("/questionnaire/create")
                        .content(MockUtil.asJsonString(testQuestionnaireVO)))
                .andDo(print())
                .andExpect(status().isForbidden());

        verify(questionnaireService, never()).create(any());
    }

    @Test
    @DisplayName("修改问卷 - 成功")
    void update_Success() throws Exception {
        // Given
        testQuestionnaireVO.setId(1L);
        when(questionnaireService.update(any(QuestionnaireDTO.class)))
                .thenReturn(testQuestionnaire);

        // When & Then
        mockMvc.perform(MockUtil.postWithAdminToken("/questionnaire/update")
                        .content(MockUtil.asJsonString(testQuestionnaireVO)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value(1));

        verify(questionnaireService).update(any(QuestionnaireDTO.class));
    }

    @Test
    @DisplayName("修改问卷 - ID为空")
    void update_NullId() throws Exception {
        // Given
        testQuestionnaireVO.setId(null);

        // When & Then
        mockMvc.perform(MockUtil.postWithAdminToken("/questionnaire/update")
                        .content(MockUtil.asJsonString(testQuestionnaireVO)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.msg").value("问卷ID不能为空"));

        verify(questionnaireService, never()).update(any());
    }

    @Test
    @DisplayName("删除问卷 - 成功")
    void delete_Success() throws Exception {
        // Given
        when(questionnaireService.deleteById(1L)).thenReturn(true);

        // When & Then
        mockMvc.perform(MockUtil.postWithAdminToken("/questionnaire/delete")
                        .param("id", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.msg").value("删除成功"));

        verify(questionnaireService).deleteById(1L);
    }

    @Test
    @DisplayName("删除问卷 - 删除失败")
    void delete_Failed() throws Exception {
        // Given
        when(questionnaireService.deleteById(1L)).thenReturn(false);

        // When & Then
        mockMvc.perform(MockUtil.postWithAdminToken("/questionnaire/delete")
                        .param("id", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.msg").value("删除失败"));

        verify(questionnaireService).deleteById(1L);
    }

    @Test
    @DisplayName("删除问卷 - 业务异常")
    void delete_BusinessException() throws Exception {
        // Given
        when(questionnaireService.deleteById(1L))
                .thenThrow(new BusinessException("问卷不存在"));

        // When & Then
        mockMvc.perform(MockUtil.postWithAdminToken("/questionnaire/delete")
                        .param("id", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.msg").value("问卷不存在"));

        verify(questionnaireService).deleteById(1L);
    }

    @Test
    @DisplayName("列出所有问卷 - 成功")
    void list_Success() throws Exception {
        // Given
        when(questionnaireService.getAll()).thenReturn(testQuestionnaireList);

        // When & Then
        mockMvc.perform(MockUtil.getWithAdminToken("/questionnaire/list"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].id").value(1))
                .andExpect(jsonPath("$.data[0].title").value("测试问卷"));

        verify(questionnaireService).getAll();
    }

    @Test
    @DisplayName("列出所有问卷 - 业务异常")
    void list_BusinessException() throws Exception {
        // Given
        when(questionnaireService.getAll())
                .thenThrow(new BusinessException("获取问卷列表失败"));

        // When & Then
        mockMvc.perform(MockUtil.getWithAdminToken("/questionnaire/list"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.msg").value("获取问卷列表失败"));

        verify(questionnaireService).getAll();
    }

    @Test
    @DisplayName("问卷详情 - 成功")
    void detail_Success() throws Exception {
        // Given
        when(questionnaireService.getById(1L)).thenReturn(testQuestionnaire);
        
        List<QuestionnairePageDTO> pages = new ArrayList<>();
        QuestionnairePageDTO page = TestDataFactory.createQuestionnairePageDTO();
        pages.add(page);
        when(questionnairePageService.getByQuestionnaireId(1L)).thenReturn(pages);
        
        List<QuestionDTO> questions = new ArrayList<>();
        QuestionDTO question = TestDataFactory.createQuestionDTO();
        questions.add(question);
        when(questionService.getByPageId(1L)).thenReturn(questions);
        
        List<QuestionOptionDTO> options = new ArrayList<>();
        QuestionOptionDTO option = TestDataFactory.createQuestionOptionDTO();
        options.add(option);
        when(questionOptionService.getByQuestionId(1L)).thenReturn(options);

        // When & Then
        mockMvc.perform(MockUtil.getWithStudentToken("/questionnaire/detail")
                        .param("id", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.title").value("测试问卷"))
                .andExpect(jsonPath("$.data.pages").isArray())
                .andExpect(jsonPath("$.data.pages[0].questions").isArray());

        verify(questionnaireService).getById(1L);
        verify(questionnairePageService).getByQuestionnaireId(1L);
        verify(questionService).getByPageId(1L);
        verify(questionOptionService).getByQuestionId(1L);
    }

    @Test
    @DisplayName("问卷详情 - 问卷不存在")
    void detail_QuestionnaireNotFound() throws Exception {
        // Given
        when(questionnaireService.getById(999L))
                .thenThrow(new BusinessException("问卷不存在"));

        // When & Then
        mockMvc.perform(MockUtil.getWithStudentToken("/questionnaire/detail")
                        .param("id", "999"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.msg").value("问卷不存在"));

        verify(questionnaireService).getById(999L);
        verify(questionnairePageService, never()).getByQuestionnaireId(any());
    }

    @Test
    @DisplayName("问卷详情 - 无权限访问")
    void detail_NoPermission() throws Exception {
        // When & Then
        mockMvc.perform(MockUtil.getWithoutToken("/questionnaire/detail")
                        .param("id", "1"))
                .andDo(print())
                .andExpect(status().isForbidden());

        verify(questionnaireService, never()).getById(any());
    }
}
