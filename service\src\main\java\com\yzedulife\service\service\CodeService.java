package com.yzedulife.service.service;

import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.CodeDTO;

/**
 * 验证码服务接口
 */
public interface CodeService {

    /**
     * 创建验证码
     * @param phone 手机号
     * @param code 验证码
     * @param expireSeconds 过期时间（秒）
     * @return 验证码DTO
     * @throws BusinessException 业务异常
     */
    CodeDTO create(String phone, String code, Long expireSeconds) throws BusinessException;

    /**
     * 查询验证码
     * @param phone 手机号
     * @return 验证码DTO
     * @throws BusinessException 业务异常
     */
    CodeDTO query(String phone) throws BusinessException;

    /**
     * 删除验证码
     * @param phone 手机号
     * @return 是否删除成功
     * @throws BusinessException 业务异常
     */
    Boolean delete(String phone) throws BusinessException;

    /**
     * 验证验证码是否有效
     * @param phone 手机号
     * @param code 验证码
     * @return 是否有效
     * @throws BusinessException 业务异常
     */
    Boolean validate(String phone, String code) throws BusinessException;
}
